#!/usr/bin/env python3
"""
Test script to verify notebook functionality before running the actual notebook.
This script tests all the key components that the notebook relies on.
"""

import sys
import os
import asyncio
import json
import time
from typing import List, Dict, Any


def test_basic_imports():
    """Test basic Python imports"""
    print("🧪 Testing basic imports...")
    try:
        from dotenv import load_dotenv
        import asyncio
        import json
        print("✅ Basic imports successful")
        return True
    except ImportError as e:
        print(f"❌ Basic import failed: {e}")
        return False


def test_environment_setup():
    """Test environment and path setup"""
    print("\n🧪 Testing environment setup...")
    try:
        from dotenv import load_dotenv
        load_dotenv()

        # Add src directory to path
        current_dir = os.getcwd()
        if 'notebooks' in current_dir:
            src_path = os.path.join(os.path.dirname(current_dir), 'src')
        else:
            src_path = os.path.join(current_dir, 'src')

        if src_path not in sys.path:
            sys.path.append(src_path)

        print(f"📂 Current directory: {current_dir}")
        print(f"📂 Src path added: {src_path}")
        print(f"📂 Src path exists: {os.path.exists(src_path)}")

        # Check if model_client.py exists
        model_client_path = os.path.join(src_path, 'model_client.py')
        print(f"📂 model_client.py exists: {os.path.exists(model_client_path)}")

        return True
    except Exception as e:
        print(f"❌ Environment setup failed: {e}")
        return False


def test_model_client_import():
    """Test importing the model client"""
    print("\n🧪 Testing model_client imports...")
    try:
        from model_client import (
            QuietEuriChatCompletionClient,
            AVAILABLE_MODELS,
            DSA_OPTIMIZED_MODELS,
            create_quiet_euri_client,
            get_dsa_optimized_model,
            create_dsa_client
        )
        print("✅ Successfully imported model_client.py")
        print(f"📊 Available models: {len(AVAILABLE_MODELS)}")
        print(f"📊 DSA optimized models: {len(DSA_OPTIMIZED_MODELS)}")
        return True, AVAILABLE_MODELS, DSA_OPTIMIZED_MODELS
    except ImportError as e:
        print(f"❌ model_client import failed: {e}")
        return False, {}, {}


def test_api_key():
    """Test API key configuration"""
    print("\n🧪 Testing API key configuration...")
    api_key = os.getenv("EURI_API_KEY")
    if api_key:
        print(f"✅ EURI_API_KEY found (length: {len(api_key)})")
        return True
    else:
        print("❌ EURI_API_KEY not found in environment variables")
        print("🔧 Please add EURI_API_KEY to your .env file")
        return False


def test_autogen_imports():
    """Test AutoGen imports"""
    print("\n🧪 Testing AutoGen imports...")
    try:
        from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
        from autogen_agentchat.messages import TextMessage
        from autogen_core.models import LLMMessage
        print("✅ AutoGen imports successful")
        print("📦 Available agents: AssistantAgent, UserProxyAgent")
        return True
    except ImportError as e:
        print(f"❌ AutoGen import failed: {e}")
        print("🔧 Make sure AutoGen 0.4+ is installed")
        return False


def test_model_client_creation(available_models):
    """Test creating model clients"""
    print("\n🧪 Testing model client creation...")
    if not available_models:
        print("❌ No available models to test")
        return False, {}

    # Test with a few key models
    test_models = [
        "gpt-4.1-nano",
        "gpt-4.1-mini",
        "openai/gpt-4o",
        "deepseek-r1-distill-llama-70b"
    ]

    clients = {}
    api_key = os.getenv("EURI_API_KEY")

    if not api_key:
        print("❌ Cannot test client creation without API key")
        return False, {}

    try:
        from model_client import create_quiet_euri_client

        for model in test_models:
            if model in available_models:
                try:
                    client = create_quiet_euri_client(model)
                    clients[model] = client
                    print(f"✅ Created client for {model}")
                    print(f"   Model Name: {client.model_name}")
                    print(f"   Description: {client.model_description}")
                except Exception as e:
                    print(f"❌ Failed to create client for {model}: {e}")
            else:
                print(f"⚠️ Model {model} not in available models")

        print(f"\n📊 Successfully created {len(clients)} clients")
        return True, clients

    except Exception as e:
        print(f"❌ Client creation test failed: {e}")
        return False, {}


def test_dsa_optimization(dsa_models):
    """Test DSA optimization functions"""
    print("\n🧪 Testing DSA optimization...")
    if not dsa_models:
        print("❌ No DSA models to test")
        return False

    try:
        from model_client import get_dsa_optimized_model, create_dsa_client

        test_scenarios = [
            {"problem_type": "mathematical", "complexity": "medium"},
            {"problem_type": "reasoning", "complexity": "hard"},
            {"problem_type": "coding", "complexity": "easy"},
            {"problem_type": "general", "complexity": "expert"}
        ]

        for scenario in test_scenarios:
            recommended_model = get_dsa_optimized_model(
                problem_type=scenario["problem_type"],
                complexity=scenario["complexity"]
            )
            print(
                f"✅ {scenario['problem_type']} ({scenario['complexity']}) -> {recommended_model}")

        return True

    except Exception as e:
        print(f"❌ DSA optimization test failed: {e}")
        return False


def test_async_functionality():
    """Test async functionality"""
    print("\n🧪 Testing async functionality...")
    try:
        async def simple_async_test():
            await asyncio.sleep(0.1)
            return "async works"

        result = asyncio.run(simple_async_test())
        print(f"✅ Async test successful: {result}")
        return True

    except Exception as e:
        print(f"❌ Async test failed: {e}")
        return False


def test_file_operations():
    """Test file operations for saving results"""
    print("\n🧪 Testing file operations...")
    try:
        # Test creating data directory
        data_dir = 'data' if 'notebooks' not in os.getcwd() else '../data'
        os.makedirs(data_dir, exist_ok=True)

        # Test saving a simple JSON file
        test_data = {"test": "data", "timestamp": time.time()}
        test_file = os.path.join(data_dir, 'test_results.json')

        with open(test_file, 'w') as f:
            json.dump(test_data, f, indent=2)

        # Test reading it back
        with open(test_file, 'r') as f:
            loaded_data = json.load(f)

        # Clean up
        os.remove(test_file)

        print(f"✅ File operations successful")
        print(f"   Data directory: {data_dir}")
        return True

    except Exception as e:
        print(f"❌ File operations test failed: {e}")
        return False


def main():
    """Run all tests"""
    print("🚀 TESTING NOTEBOOK FUNCTIONALITY")
    print("=" * 50)

    test_results = {}

    # Run all tests
    test_results["basic_imports"] = test_basic_imports()
    test_results["environment_setup"] = test_environment_setup()

    model_import_success, available_models, dsa_models = test_model_client_import()
    test_results["model_client_import"] = model_import_success

    test_results["api_key"] = test_api_key()
    test_results["autogen_imports"] = test_autogen_imports()

    client_success, clients = test_model_client_creation(available_models)
    test_results["model_client_creation"] = client_success

    test_results["dsa_optimization"] = test_dsa_optimization(dsa_models)
    test_results["async_functionality"] = test_async_functionality()
    test_results["file_operations"] = test_file_operations()

    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)

    passed = 0
    total = len(test_results)

    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1

    print(f"\n🎯 Overall: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! The notebook should work correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Please fix the issues before running the notebook.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
