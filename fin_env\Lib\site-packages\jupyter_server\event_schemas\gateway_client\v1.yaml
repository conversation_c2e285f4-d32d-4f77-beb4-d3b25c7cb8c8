"$id": https://events.jupyter.org/jupyter_server/gateway_client/v1
version: "1"
title: Gateway Client activities.
personal-data: true
description: |
  Record events of a gateway client.
type: object
required:
  - status
  - msg
properties:
  status:
    enum:
      - error
      - success
    description: |
      Status received by Gateway client based on the rest api operation to gateway kernel.

      This is a required field.

      Possible values:

      1. error
         Error response from a rest api operation to gateway kernel.

      2. success
         Success response from a rest api operation to gateway kernel.
  status_code:
    type: number
    description: |
      Http response codes from a rest api operation to gateway kernel.
      Examples: 200, 400, 502, 503, 599 etc.
  msg:
    type: string
    description: |
      Description of the event being emitted.
  gateway_url:
    type: string
    description: |
      Gateway url where the remote server exist.
