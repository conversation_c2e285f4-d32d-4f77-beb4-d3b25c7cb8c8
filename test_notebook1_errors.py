#!/usr/bin/env python3
"""
Quick test to identify remaining errors in notebook 1
"""

import sys
import os
import asyncio
import json
import time
from typing import List, Dict, Any

def test_notebook_imports():
    """Test the key imports from notebook 1"""
    print("🧪 Testing notebook 1 imports...")
    
    # Test basic imports
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ dotenv import successful")
    except Exception as e:
        print(f"❌ dotenv import failed: {e}")
        return False
    
    # Test path setup
    try:
        current_dir = os.getcwd()
        if 'notebooks' in current_dir:
            src_path = os.path.join(os.path.dirname(current_dir), 'src')
        else:
            src_path = os.path.join(current_dir, 'src')
        
        if src_path not in sys.path:
            sys.path.append(src_path)
        
        print(f"✅ Path setup successful: {src_path}")
    except Exception as e:
        print(f"❌ Path setup failed: {e}")
        return False
    
    # Test model_client import
    try:
        from model_client import (
            QuietEuriChatCompletionClient,
            AVAILABLE_MODELS,
            DSA_OPTIMIZED_MODELS,
            create_quiet_euri_client,
            get_dsa_optimized_model,
            create_dsa_client
        )
        print("✅ model_client import successful")
        print(f"   Available models: {len(AVAILABLE_MODELS)}")
    except Exception as e:
        print(f"❌ model_client import failed: {e}")
        return False
    
    # Test AutoGen imports
    try:
        from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
        from autogen_agentchat.messages import TextMessage
        from autogen_core.models import LLMMessage
        print("✅ AutoGen imports successful")
    except Exception as e:
        print(f"❌ AutoGen import failed: {e}")
        return False
    
    return True

def test_api_key():
    """Test API key availability"""
    print("\n🧪 Testing API key...")
    api_key = os.getenv("EURI_API_KEY")
    if api_key:
        print(f"✅ EURI_API_KEY found (length: {len(api_key)})")
        return True
    else:
        print("❌ EURI_API_KEY not found")
        return False

def test_client_creation():
    """Test creating a simple client"""
    print("\n🧪 Testing client creation...")
    
    if not os.getenv("EURI_API_KEY"):
        print("❌ Cannot test client creation without API key")
        return False
    
    try:
        from model_client import create_quiet_euri_client
        client = create_quiet_euri_client("gpt-4.1-nano")
        print(f"✅ Client created successfully: {client.model_name}")
        return True
    except Exception as e:
        print(f"❌ Client creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_agent_creation():
    """Test creating an AutoGen agent"""
    print("\n🧪 Testing AutoGen agent creation...")
    
    if not os.getenv("EURI_API_KEY"):
        print("❌ Cannot test agent creation without API key")
        return False
    
    try:
        from model_client import create_quiet_euri_client
        from autogen_agentchat.agents import AssistantAgent
        
        client = create_quiet_euri_client("gpt-4.1-nano")
        
        # Try to create an agent
        agent = AssistantAgent(
            name="TestAgent",
            model_client=client,
            system_message="You are a test agent."
        )
        
        print(f"✅ Agent created successfully: {agent.name}")
        return True
        
    except Exception as e:
        print(f"❌ Agent creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_async_functionality():
    """Test async operations"""
    print("\n🧪 Testing async functionality...")
    
    try:
        async def simple_test():
            await asyncio.sleep(0.1)
            return "success"
        
        result = asyncio.run(simple_test())
        print(f"✅ Async test successful: {result}")
        return True
        
    except Exception as e:
        print(f"❌ Async test failed: {e}")
        return False

def test_conversation():
    """Test a simple conversation"""
    print("\n🧪 Testing conversation...")
    
    if not os.getenv("EURI_API_KEY"):
        print("❌ Cannot test conversation without API key")
        return False
    
    try:
        from model_client import create_quiet_euri_client
        from autogen_agentchat.agents import AssistantAgent
        from autogen_agentchat.messages import TextMessage
        from autogen_core.models import LLMMessage
        
        client = create_quiet_euri_client("gpt-4.1-nano")
        
        # Test direct client call
        messages = [LLMMessage(content="Hello, what is 2+2?", source="user")]
        result = asyncio.run(client.create(messages))
        
        print(f"✅ Direct client call successful")
        print(f"   Response: {result.content[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ Conversation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 TESTING NOTEBOOK 1 FOR ERRORS")
    print("=" * 50)
    
    tests = [
        ("Imports", test_notebook_imports),
        ("API Key", test_api_key),
        ("Client Creation", test_client_creation),
        ("Agent Creation", test_agent_creation),
        ("Async Functionality", test_async_functionality),
        ("Conversation", test_conversation)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed != total:
        print("\n⚠️ ERRORS FOUND - These need to be fixed in notebook 1:")
        for test_name, result in results.items():
            if not result:
                print(f"   - {test_name}")
    else:
        print("🎉 All tests passed! Notebook 1 should work correctly.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
