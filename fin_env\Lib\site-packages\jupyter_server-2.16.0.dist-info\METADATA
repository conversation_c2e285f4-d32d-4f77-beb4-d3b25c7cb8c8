Metadata-Version: 2.4
Name: jupyter_server
Version: 2.16.0
Summary: The backend—i.e. core services, APIs, and REST endpoints—to Jupyter web applications.
Project-URL: Homepage, https://jupyter-server.readthedocs.io
Project-URL: Documentation, https://jupyter-server.readthedocs.io
Project-URL: Funding, https://jupyter.org/about#donate
Project-URL: Source, https://github.com/jupyter-server/jupyter_server
Project-URL: Tracker, https://github.com/jupyter-server/jupyter_server/issues
Author-email: Jupyter Development Team <<EMAIL>>
License: BSD 3-Clause License
        
        - Copyright (c) 2001-2015, IPython Development Team
        - Copyright (c) 2015-, Jupyter Development Team
        
        All rights reserved.
        
        Redistribution and use in source and binary forms, with or without
        modification, are permitted provided that the following conditions are met:
        
        1. Redistributions of source code must retain the above copyright notice, this
           list of conditions and the following disclaimer.
        
        2. Redistributions in binary form must reproduce the above copyright notice,
           this list of conditions and the following disclaimer in the documentation
           and/or other materials provided with the distribution.
        
        3. Neither the name of the copyright holder nor the names of its
           contributors may be used to endorse or promote products derived from
           this software without specific prior written permission.
        
        THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
        AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
        IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
        DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
        FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
        DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
        SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
        CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
        OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
        OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
License-File: LICENSE
Keywords: ipython,jupyter
Classifier: Development Status :: 5 - Production/Stable
Classifier: Framework :: Jupyter
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: Intended Audience :: System Administrators
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Requires-Python: >=3.9
Requires-Dist: anyio>=3.1.0
Requires-Dist: argon2-cffi>=21.1
Requires-Dist: jinja2>=3.0.3
Requires-Dist: jupyter-client>=7.4.4
Requires-Dist: jupyter-core!=5.0.*,>=4.12
Requires-Dist: jupyter-events>=0.11.0
Requires-Dist: jupyter-server-terminals>=0.4.4
Requires-Dist: nbconvert>=6.4.4
Requires-Dist: nbformat>=5.3.0
Requires-Dist: overrides>=5.0
Requires-Dist: packaging>=22.0
Requires-Dist: prometheus-client>=0.9
Requires-Dist: pywinpty>=2.0.1; os_name == 'nt'
Requires-Dist: pyzmq>=24
Requires-Dist: send2trash>=1.8.2
Requires-Dist: terminado>=0.8.3
Requires-Dist: tornado>=6.2.0
Requires-Dist: traitlets>=5.6.0
Requires-Dist: websocket-client>=1.7
Provides-Extra: docs
Requires-Dist: ipykernel; extra == 'docs'
Requires-Dist: jinja2; extra == 'docs'
Requires-Dist: jupyter-client; extra == 'docs'
Requires-Dist: myst-parser; extra == 'docs'
Requires-Dist: nbformat; extra == 'docs'
Requires-Dist: prometheus-client; extra == 'docs'
Requires-Dist: pydata-sphinx-theme; extra == 'docs'
Requires-Dist: send2trash; extra == 'docs'
Requires-Dist: sphinx-autodoc-typehints; extra == 'docs'
Requires-Dist: sphinxcontrib-github-alt; extra == 'docs'
Requires-Dist: sphinxcontrib-openapi>=0.8.0; extra == 'docs'
Requires-Dist: sphinxcontrib-spelling; extra == 'docs'
Requires-Dist: sphinxemoji; extra == 'docs'
Requires-Dist: tornado; extra == 'docs'
Requires-Dist: typing-extensions; extra == 'docs'
Provides-Extra: test
Requires-Dist: flaky; extra == 'test'
Requires-Dist: ipykernel; extra == 'test'
Requires-Dist: pre-commit; extra == 'test'
Requires-Dist: pytest-console-scripts; extra == 'test'
Requires-Dist: pytest-jupyter[server]>=0.7; extra == 'test'
Requires-Dist: pytest-timeout; extra == 'test'
Requires-Dist: pytest<9,>=7.0; extra == 'test'
Requires-Dist: requests; extra == 'test'
Description-Content-Type: text/markdown

# Jupyter Server

[![Build Status](https://github.com/jupyter-server/jupyter_server/actions/workflows/python-tests.yml/badge.svg?query=branch%3Amain++)](https://github.com/jupyter-server/jupyter_server/actions/workflows/python-tests.yml/badge.svg?query=branch%3Amain++)
[![Documentation Status](https://readthedocs.org/projects/jupyter-server/badge/?version=latest)](http://jupyter-server.readthedocs.io/en/latest/?badge=latest)

The Jupyter Server provides the backend (i.e. the core services, APIs, and REST endpoints) for Jupyter web applications like Jupyter notebook, JupyterLab, and Voila.

For more information, read our [documentation here](http://jupyter-server.readthedocs.io/en/latest/?badge=latest).

## Installation and Basic usage

To install the latest release locally, make sure you have
[pip installed](https://pip.readthedocs.io/en/stable/installing/) and run:

```
pip install jupyter_server
```

Jupyter Server currently supports Python>=3.6 on Linux, OSX and Windows.

### Versioning and Branches

If Jupyter Server is a dependency of your project/application, it is important that you pin it to a version that works for your application. Currently, Jupyter Server only has minor and patch versions. Different minor versions likely include API-changes while patch versions do not change API.

When a new minor version is released on PyPI, a branch for that version will be created in this repository, and the version of the main branch will be bumped to the next minor version number. That way, the main branch always reflects the latest un-released version.

To see the changes between releases, checkout the [CHANGELOG](https://github.com/jupyter/jupyter_server/blob/main/CHANGELOG.md).

## Usage - Running Jupyter Server

### Running in a local installation

Launch with:

```
jupyter server
```

### Testing

See [CONTRIBUTING](https://github.com/jupyter-server/jupyter_server/blob/main/CONTRIBUTING.rst#running-tests).

## Contributing

If you are interested in contributing to the project, see [`CONTRIBUTING.rst`](CONTRIBUTING.rst).

## Team Meetings and Roadmap

- When: Thursdays [8:00am, Pacific time](https://www.thetimezoneconverter.com/?t=8%3A00%20am&tz=San%20Francisco&)
- Where: [Jovyan Zoom](https://zoom.us/j/95228013874?pwd=Ep7HIk8t9JP6VToxt1Wj4P7K5PshC0.1)
- What:
  - [Meeting notes](https://github.com/jupyter-server/team-compass/issues?q=is%3Aissue%20%20Meeting%20Notes%20)
  - [Agenda](https://hackmd.io/Wmz_wjrLRHuUbgWphjwRWw)

See our tentative [roadmap here](https://github.com/jupyter/jupyter_server/issues/127).

## About the Jupyter Development Team

The Jupyter Development Team is the set of all contributors to the Jupyter project.
This includes all of the Jupyter subprojects.

The core team that coordinates development on GitHub can be found here:
https://github.com/jupyter/.

## Our Copyright Policy

Jupyter uses a shared copyright model. Each contributor maintains copyright
over their contributions to Jupyter. But, it is important to note that these
contributions are typically only changes to the repositories. Thus, the Jupyter
source code, in its entirety is not the copyright of any single person or
institution. Instead, it is the collective copyright of the entire Jupyter
Development Team. If individual contributors want to maintain a record of what
changes/contributions they have specific copyright on, they should indicate
their copyright in the commit message of the change, when they commit the
change to one of the Jupyter repositories.

With this in mind, the following banner should be used in any source code file
to indicate the copyright and license terms:

```
# Copyright (c) Jupyter Development Team.
# Distributed under the terms of the Modified BSD License.
```
