$schema: http://json-schema.org/draft-07/schema
$id: http://event.jupyter.org/property-metaschema
version: "1"
title: Property Metaschema
description: |
  A metaschema for validating properties within
  an event schema

properties:
  title:
    type: string
  description:
    type: string
  properties:
    type: object
    additionalProperties:
      $ref: http://event.jupyter.org/property-metaschema
    propertyNames:
      pattern: ^(?!__.*)

  items:
    $ref: http://event.jupyter.org/property-metaschema

additionalProperties:
  $ref: http://event.jupyter.org/property-metaschema

propertyNames:
  pattern: ^(?!__.*)
