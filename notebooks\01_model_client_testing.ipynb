# Install required packages if not already installed
!pip install pyautogen>=0.4.0 autogen-agentchat>=0.4.0 autogen-core>=0.4.0
!pip install euriai python-dotenv asyncio
!pip install jupyter ipywidgets  # For interactive widgets

import sys
import os
import asyncio
import json
import time
from typing import List, Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src directory to path to import our model_client
# First, get the notebook's parent directory
notebook_dir = os.path.dirname(os.path.abspath(''))
if 'notebooks' in notebook_dir:
    # If we're in notebooks folder, go up one level
    project_root = os.path.dirname(notebook_dir)
else:
    # If we're in project root
    project_root = notebook_dir

src_path = os.path.join(project_root, 'src')
print(f"📂 Project root: {project_root}")
print(f"📂 Looking for model_client.py in: {src_path}")

# Add to Python path
if src_path not in sys.path:
    sys.path.insert(0, src_path)

# Also add current directory
if '.' not in sys.path:
    sys.path.insert(0, '.')

print(f"🐍 Python path updated")
print(f"📍 Current working directory: {os.getcwd()}")

# Check if model_client.py exists
model_client_path = os.path.join(src_path, 'model_client.py')
if os.path.exists(model_client_path):
    print(f"✅ Found model_client.py at: {model_client_path}")
else:
    print(f"❌ model_client.py not found at: {model_client_path}")
    
    # Alternative locations to check
    alternative_paths = [
        './model_client.py',
        '../model_client.py', 
        './src/model_client.py',
        '../src/model_client.py'
    ]
    
    print("\n🔍 Checking alternative locations:")
    for alt_path in alternative_paths:
        if os.path.exists(alt_path):
            print(f"✅ Found at: {alt_path}")
            # Add this directory to path
            alt_dir = os.path.dirname(os.path.abspath(alt_path))
            if alt_dir not in sys.path:
                sys.path.insert(0, alt_dir)
            break
        else:
            print(f"❌ Not found: {alt_path}")

# Test importing our custom model client
try:
    from model_client import (
        QuietEuriChatCompletionClient,
        AVAILABLE_MODELS,
        DSA_OPTIMIZED_MODELS,
        create_quiet_euri_client,
        get_dsa_optimized_model,
        create_dsa_client
    )
    print("✅ Successfully imported model_client.py")
    print(f"📊 Available models: {len(AVAILABLE_MODELS)}")
    
except ImportError as e:
    print(f"❌ Failed to import model_client.py: {e}")
    
    # Debug information
    print("\n🔍 Debug Information:")
    print(f"Python path: {sys.path[:3]}...")  # Show first 3 entries
    print(f"Current directory contents: {os.listdir('.')}")
    
    # Try to find the file
    for root, dirs, files in os.walk('.'):
        if 'model_client.py' in files:
            print(f"Found model_client.py in: {root}")
    
    print("\n💡 Solutions:")
    print("1. Make sure model_client.py is in the src/ directory")
    print("2. Or place model_client.py in the same directory as this notebook")
    print("3. Or update the path in the sys.path.append() line above")

# Test EURI_API_KEY environment variable
api_key = os.getenv("EURI_API_KEY")
if api_key:
    print(f"✅ EURI_API_KEY found (length: {len(api_key)})")
else:
    print("❌ EURI_API_KEY not found in environment variables")
    print("🔧 Please add EURI_API_KEY to your .env file")
    
    # Option to set it manually for testing
    manual_key = input("Enter your EURI API key for testing (or press Enter to skip): ")
    if manual_key:
        os.environ["EURI_API_KEY"] = manual_key
        print("🔑 API key set manually for this session")
    else:
        print("⚠️ Continuing without API key - some tests may fail")

# Display all available models with details
print("🤖 Available Models from model_client.py:")
print("=" * 60)

for model_id, config in AVAILABLE_MODELS.items():
    print(f"\n📋 {config['name']}")
    print(f"   ID: {model_id}")
    print(f"   Family: {config['family']}")
    print(f"   Description: {config['description']}")
    print(f"   Vision: {'✅' if config['vision'] else '❌'}")
    print(f"   Function Calling: {'✅' if config['function_calling'] else '❌'}")
    print(f"   Context Length: {config['context_length']:,} tokens")
    print(f"   Tags: {', '.join(config['tags'])}")

print(f"\n📊 Total Models Available: {len(AVAILABLE_MODELS)}")

# Test the built-in model listing function
print("\n🎨 Using built-in model listing function:")
QuietEuriChatCompletionClient.print_available_models()

# Test creating clients with different models - CORRECTED VERSION
test_models = [
    "gpt-4.1-nano",  # Fast, efficient
    "gpt-4.1-mini",  # Balanced
    "openai/gpt-4o", # Advanced
    "deepseek-r1-distill-llama-70b"  # Code/math specialist
]

clients = {}

for model in test_models:
    try:
        client = create_quiet_euri_client(model)
        clients[model] = client
        
        print(f"✅ Created client for {model}")
        print(f"   Model Name: {client.model_name}")
        print(f"   Description: {client.model_description}")
        
        # Fixed: Properly access capabilities
        try:
            capabilities = client.capabilities
            if hasattr(capabilities, 'vision'):
                # It's a ModelInfo object
                vision = capabilities.vision
                function_calling = capabilities.function_calling
            elif isinstance(capabilities, dict):
                # It's a dictionary
                vision = capabilities.get('vision', False)
                function_calling = capabilities.get('function_calling', False)
            else:
                # Fallback: get from model config directly
                model_config = client._model_config
                vision = model_config.get('vision', False)
                function_calling = model_config.get('function_calling', False)
            
            print(f"   Capabilities: Vision={'✅' if vision else '❌'}, Function Calling={'✅' if function_calling else '❌'}")
            
        except Exception as cap_error:
            print(f"   Capabilities: Unable to retrieve ({cap_error})")
        
        print()
        
    except Exception as e:
        print(f"❌ Failed to create client for {model}: {e}")
        print()

print(f"📊 Successfully created {len(clients)} model clients")

from autogen import ConversableAgent

assistant = ConversableAgent(
    name="assistant",
    llm_config={
        "config_list": [
            {
                "model": "openai/gpt-4o",
                "client": create_quiet_euri_client("openai/gpt-4o")
            }
        ],
        "temperature": 0.1,
    },
)

# Test importing our custom model client
try:
    from model_client import (
        QuietEuriChatCompletionClient,
        AVAILABLE_MODELS,
        DSA_OPTIMIZED_MODELS,
        create_quiet_euri_client,
        get_dsa_optimized_model,
        create_dsa_client
    )
    print("✅ Successfully imported model_client.py")
    print(f"📊 Available models: {len(AVAILABLE_MODELS)}")
    
except ImportError as e:
    print(f"❌ Failed to import model_client.py: {e}")
    
    # Debug information
    print("\n🔍 Debug Information:")
    print(f"Python path: {sys.path[:3]}...")  # Show first 3 entries
    print(f"Current directory contents: {os.listdir('.')}")
    
    # Try to find the file
    for root, dirs, files in os.walk('.'):
        if 'model_client.py' in files:
            print(f"Found model_client.py in: {root}")
    
    print("\n💡 Solutions:")
    print("1. Make sure model_client.py is in the src/ directory")
    print("2. Or place model_client.py in the same directory as this notebook")
    print("3. Or update the path in the sys.path.append() line above")

# Test importing our custom model client
try:
    from model_client import (
        QuietEuriChatCompletionClient,
        AVAILABLE_MODELS,
        DSA_OPTIMIZED_MODELS,
        create_quiet_euri_client,
        get_dsa_optimized_model,
        create_dsa_client
    )
    print("✅ Successfully imported model_client.py")
    print(f"📊 Available models: {len(AVAILABLE_MODELS)}")
    
except ImportError as e:
    print(f"❌ Failed to import model_client.py: {e}")
    
    # Debug information
    print("\n🔍 Debug Information:")
    print(f"Python path: {sys.path[:3]}...")  # Show first 3 entries
    print(f"Current directory contents: {os.listdir('.')}")
    
    # Try to find the file
    for root, dirs, files in os.walk('.'):
        if 'model_client.py' in files:
            print(f"Found model_client.py in: {root}")
    
    print("\n💡 Solutions:")
    print("1. Make sure model_client.py is in the src/ directory")
    print("2. Or place model_client.py in the same directory as this notebook")
    print("3. Or update the path in the sys.path.append() line above")

# Test importing our custom model client
try:
    from model_client import (
        QuietEuriChatCompletionClient,
        AVAILABLE_MODELS,
        DSA_OPTIMIZED_MODELS,
        create_quiet_euri_client,
        get_dsa_optimized_model,
        create_dsa_client
    )
    print("✅ Successfully imported model_client.py")
    print(f"📊 Available models: {len(AVAILABLE_MODELS)}")
    
except ImportError as e:
    print(f"❌ Failed to import model_client.py: {e}")
    
    # Debug information
    print("\n🔍 Debug Information:")
    print(f"Python path: {sys.path[:3]}...")  # Show first 3 entries
    print(f"Current directory contents: {os.listdir('.')}")
    
    # Try to find the file
    for root, dirs, files in os.walk('.'):
        if 'model_client.py' in files:
            print(f"Found model_client.py in: {root}")
    
    print("\n💡 Solutions:")
    print("1. Make sure model_client.py is in the src/ directory")
    print("2. Or place model_client.py in the same directory as this notebook")
    print("3. Or update the path in the sys.path.append() line above")

# Test importing our custom model client
try:
    from model_client import (
        QuietEuriChatCompletionClient,
        AVAILABLE_MODELS,
        DSA_OPTIMIZED_MODELS,
        create_quiet_euri_client,
        get_dsa_optimized_model,
        create_dsa_client
    )
    print("✅ Successfully imported model_client.py")
    print(f"📊 Available models: {len(AVAILABLE_MODELS)}")
    
except ImportError as e:
    print(f"❌ Failed to import model_client.py: {e}")
    
    # Debug information
    print("\n🔍 Debug Information:")
    print(f"Python path: {sys.path[:3]}...")  # Show first 3 entries
    print(f"Current directory contents: {os.listdir('.')}")
    
    # Try to find the file
    for root, dirs, files in os.walk('.'):
        if 'model_client.py' in files:
            print(f"Found model_client.py in: {root}")
    
    print("\n💡 Solutions:")
    print("1. Make sure model_client.py is in the src/ directory")
    print("2. Or place model_client.py in the same directory as this notebook")
    print("3. Or update the path in the sys.path.append() line above")

# Create AutoGen agents using our model clients
agents = {}

# Test with a simple model first
try:
    if "gpt-4.1-nano" in clients:
        nano_client = clients["gpt-4.1-nano"]
        
        # Create a ConversableAgent with our model client
        test_agent = ConversableAgent(
            name="TestAgent",
            model_client=nano_client,
            system_message="You are a helpful AI assistant for FinSolve Technologies. Provide clear, concise responses."
        )
        
        agents["test_agent"] = test_agent
        print("✅ Created AutoGen ConversableAgent with gpt-4.1-nano")
        print(f"   Agent Name: {test_agent.name}")
        print(f"   Model Client: {type(nano_client).__name__}")
        
except Exception as e:
    print(f"❌ Failed to create AutoGen agent: {e}")
    import traceback
    traceback.print_exc()

# Test basic conversation with our agent
async def test_agent_conversation(agent, message: str):
    """Test a basic conversation with an agent"""
    try:
        print(f"🤖 Testing conversation with {agent.name}")
        print(f"👤 User: {message}")
        
        # Create a TextMessage
        user_message = TextMessage(content=message, source="user")
        
        # Get response from agent
        response = await agent.on_messages([user_message], cancellation_token=None)
        
        if response.chat_message:
            print(f"🤖 {agent.name}: {response.chat_message.content}")
            return response.chat_message.content
        else:
            print("❌ No response received")
            return None
            
    except Exception as e:
        print(f"❌ Conversation failed: {e}")
        import traceback
        traceback.print_exc()
        return None

# Test conversation if we have an agent
if "test_agent" in agents:
    test_message = "Hello! Can you tell me about FinSolve Technologies in 2 sentences?"
    response = await test_agent_conversation(agents["test_agent"], test_message)
else:
    print("⚠️ No test agent available for conversation testing")

# Test DSA-optimized model selection function
print("🧮 Testing DSA-Optimized Model Selection:")
print("=" * 50)

test_scenarios = [
    {"problem_type": "mathematical", "complexity": "medium"},
    {"problem_type": "reasoning", "complexity": "hard"},
    {"problem_type": "coding", "complexity": "easy"},
    {"problem_type": "general", "complexity": "expert"},
    {"problem_type": "optimization", "complexity": "medium"}
]

for scenario in test_scenarios:
    recommended_model = get_dsa_optimized_model(
        problem_type=scenario["problem_type"],
        complexity=scenario["complexity"]
    )
    
    model_info = AVAILABLE_MODELS.get(recommended_model, {})
    
    print(f"\n🎯 Problem: {scenario['problem_type']} ({scenario['complexity']})")
    print(f"   Recommended: {recommended_model}")
    print(f"   Model Name: {model_info.get('name', 'Unknown')}")
    print(f"   Tags: {', '.join(model_info.get('tags', []))}")

# Test creating DSA-optimized clients
print("\n🏗️ Testing DSA Client Creation:")
print("=" * 40)

dsa_clients = {}

for scenario in test_scenarios:
    try:
        client = create_dsa_client(
            problem_type=scenario["problem_type"],
            complexity=scenario["complexity"]
        )
        
        key = f"{scenario['problem_type']}_{scenario['complexity']}"
        dsa_clients[key] = client
        
        print(f"✅ Created DSA client for {key}")
        
    except Exception as e:
        print(f"❌ Failed to create DSA client for {scenario}: {e}")

print(f"\n📊 Created {len(dsa_clients)} DSA-optimized clients")

# Test model capabilities and routing decisions
print("🔀 Model Routing Analysis:")
print("=" * 40)

# Define task requirements
task_requirements = {
    "authentication": {"speed": "high", "cost": "low", "complexity": "low"},
    "document_analysis": {"speed": "medium", "cost": "medium", "complexity": "high"},
    "response_generation": {"speed": "medium", "cost": "medium", "complexity": "high"},
    "quality_validation": {"speed": "high", "cost": "low", "complexity": "medium"},
    "complex_reasoning": {"speed": "low", "cost": "high", "complexity": "expert"}
}

# Suggest optimal models for each task
task_model_suggestions = {
    "authentication": "gpt-4.1-nano",  # Fast, cheap
    "document_analysis": "deepseek-r1-distill-llama-70b",  # Good for analysis
    "response_generation": "openai/gpt-4o",  # High quality
    "quality_validation": "gpt-4.1-mini",  # Balanced
    "complex_reasoning": "anthropic/claude-sonnet-4"  # Advanced reasoning
}

for task, requirements in task_requirements.items():
    suggested_model = task_model_suggestions.get(task, "openai/gpt-4o")
    model_info = AVAILABLE_MODELS.get(suggested_model, {})
    
    print(f"\n🎯 Task: {task}")
    print(f"   Requirements: {requirements}")
    print(f"   Suggested Model: {suggested_model}")
    print(f"   Model Name: {model_info.get('name', 'Unknown')}")
    print(f"   Context Length: {model_info.get('context_length', 0):,} tokens")
    print(f"   Suitable because: {model_info.get('description', 'N/A')}")

# Test token counting and performance metrics
if clients:
    print("⚡ Performance Testing:")
    print("=" * 30)
    
    # Test message for token counting
    test_messages = [
        LLMMessage(content="What is FinSolve Technologies?", source="user"),
        LLMMessage(content="Tell me about the company's financial performance.", source="user")
    ]
    
    # Test token counting with different models
    for model_name, client in list(clients.items())[:3]:  # Test first 3 clients
        try:
            start_time = time.time()
            
            # Count tokens
            token_count = client.count_tokens(test_messages)
            remaining_tokens = client.remaining_tokens(test_messages)
            
            end_time = time.time()
            
            print(f"\n📊 {model_name}:")
            print(f"   Token Count: {token_count}")
            print(f"   Remaining Tokens: {remaining_tokens:,}")
            print(f"   Processing Time: {(end_time - start_time)*1000:.2f}ms")
            
        except Exception as e:
            print(f"❌ Performance test failed for {model_name}: {e}")
else:
    print("⚠️ No clients available for performance testing")

# Test streaming capabilities
async def test_streaming(client, message: str):
    """Test streaming response from a model client"""
    try:
        print(f"🌊 Testing streaming with {client.model_name}")
        print(f"👤 User: {message}")
        print(f"🤖 Streaming response: ", end="")
        
        messages = [LLMMessage(content=message, source="user")]
        
        async for chunk in client.create_stream(messages):
            if isinstance(chunk, str):
                print(chunk, end="", flush=True)
            else:
                # Final result
                print(f"\n\n📊 Final result received: {type(chunk)}")
                if hasattr(chunk, 'usage'):
                    print(f"   Usage: {chunk.usage}")
                break
                
    except Exception as e:
        print(f"❌ Streaming test failed: {e}")

# Test streaming with one client
if clients:
    first_client = list(clients.values())[0]
    streaming_message = "Explain FinSolve Technologies' business model in 3 sentences."
    await test_streaming(first_client, streaming_message)
else:
    print("⚠️ No clients available for streaming test")

# Interactive model comparison
import ipywidgets as widgets
from IPython.display import display, clear_output

def create_model_selector():
    """Create an interactive model selector"""
    if not clients:
        print("⚠️ No model clients available for interactive testing")
        return
    
    # Create widgets
    model_dropdown = widgets.Dropdown(
        options=list(clients.keys()),
        description='Model:',
        style={'description_width': 'initial'}
    )
    
    message_text = widgets.Textarea(
        value='Tell me about FinSolve Technologies.',
        placeholder='Enter your message here...',
        description='Message:',
        layout=widgets.Layout(width='500px', height='100px'),
        style={'description_width': 'initial'}
    )
    
    send_button = widgets.Button(
        description='Send Message',
        button_style='primary',
        icon='paper-plane'
    )
    
    output_area = widgets.Output()
    
    async def on_send_click(b):
        """Handle send button click"""
        with output_area:
            clear_output()
            selected_model = model_dropdown.value
            message = message_text.value
            
            if selected_model in clients:
                client = clients[selected_model]
                print(f"🤖 Using {client.model_name}")
                print(f"👤 Message: {message}")
                print("\n⏳ Generating response...\n")
                
                try:
                    messages = [LLMMessage(content=message, source="user")]
                    result = await client.create(messages)
                    
                    print(f"🤖 Response: {result.content}")
                    print(f"\n📊 Metrics:")
                    print(f"   Tokens Used: {result.usage.prompt_tokens + result.usage.completion_tokens}")
                    print(f"   Finish Reason: {result.finish_reason}")
                    
                except Exception as e:
                    print(f"❌ Error: {e}")
    
    send_button.on_click(lambda b: asyncio.create_task(on_send_click(b)))
    
    # Display widgets
    display(widgets.VBox([
        widgets.HTML("<h3>🧪 Interactive Model Testing</h3>"),
        model_dropdown,
        message_text,
        send_button,
        output_area
    ]))

# Create the interactive testing interface
create_model_selector()

# Generate summary report
print("📊 MODEL CLIENT TESTING SUMMARY")
print("=" * 50)

# Test results summary
test_results = {
    "Model Client Import": "✅" if 'QuietEuriChatCompletionClient' in globals() else "❌",
    "API Key Configuration": "✅" if os.getenv("EURI_API_KEY") else "❌",
    "Available Models": f"✅ {len(AVAILABLE_MODELS)} models",
    "Client Creation": f"✅ {len(clients)} clients created",
    "AutoGen Integration": "✅" if agents else "❌",
    "DSA Optimization": f"✅ {len(dsa_clients)} DSA clients"
}

print("\n🧪 Test Results:")
for test_name, result in test_results.items():
    print(f"   {test_name}: {result}")

# Model capabilities summary
print("\n🤖 Model Capabilities Summary:")
vision_models = [name for name, config in AVAILABLE_MODELS.items() if config['vision']]
function_models = [name for name, config in AVAILABLE_MODELS.items() if config['function_calling']]
large_context = [name for name, config in AVAILABLE_MODELS.items() if config['context_length'] >= 128000]

print(f"   Vision Capable: {len(vision_models)} models")
print(f"   Function Calling: {len(function_models)} models")
print(f"   Large Context (128k+): {len(large_context)} models")

# Recommendations for next steps
print("\n🚀 Next Steps Recommendations:")
print("   1. ✅ Model client integration working - proceed to document processing")
print("   2. ✅ AutoGen compatibility confirmed - ready for agent development")
print("   3. ✅ DSA optimization available - can optimize for specific tasks")
print("   4. 📝 Next notebook: 02_document_processing.ipynb")
print("   5. 🔄 Consider adding model performance benchmarking")

# Save test results for next notebook
test_summary = {
    "successful_models": list(clients.keys()),
    "recommended_models": {
        "fast_tasks": "gpt-4.1-nano",
        "balanced_tasks": "gpt-4.1-mini", 
        "complex_tasks": "openai/gpt-4o",
        "code_tasks": "deepseek-r1-distill-llama-70b"
    },
    "capabilities_verified": True,
    "autogen_compatible": bool(agents)
}

# Save to file for next notebook
with open('../data/model_test_results.json', 'w') as f:
    json.dump(test_summary, f, indent=2)
    
print("\n💾 Test results saved to ../data/model_test_results.json")
print("\n🎉 Model Client Testing Complete!")

# Troubleshooting and diagnostic information
print("🔧 TROUBLESHOOTING INFORMATION")
print("=" * 40)

# Environment check
print("\n🌍 Environment:")
print(f"   Python Version: {sys.version}")
print(f"   Working Directory: {os.getcwd()}")
print(f"   EURI_API_KEY Set: {'Yes' if os.getenv('EURI_API_KEY') else 'No'}")

# Package versions
print("\n📦 Package Versions:")
try:
    import autogen_agentchat
    print(f"   autogen-agentchat: {autogen_agentchat.__version__ if hasattr(autogen_agentchat, '__version__') else 'Unknown'}")
except:
    print("   autogen-agentchat: Not installed or import failed")

try:
    import autogen_core  
    print(f"   autogen-core: {autogen_core.__version__ if hasattr(autogen_core, '__version__') else 'Unknown'}")
except:
    print("   autogen-core: Not installed or import failed")

try:
    import euriai
    print(f"   euriai: Available")
except:
    print("   euriai: Not installed or import failed")

# Common issues and solutions
print("\n🚨 Common Issues & Solutions:")
print("   1. Import Error: Make sure model_client.py is in ../src/ directory")
print("   2. API Key Error: Set EURI_API_KEY in .env file")
print("   3. AutoGen Error: Install autogen>=0.4.0 and autogen-agentchat>=0.4.0")
print("   4. Conversation Error: Check model availability and API key")
print("   5. Streaming Error: Some models may not support streaming")

print("\n✅ If all tests passed, you're ready for the next notebook!")