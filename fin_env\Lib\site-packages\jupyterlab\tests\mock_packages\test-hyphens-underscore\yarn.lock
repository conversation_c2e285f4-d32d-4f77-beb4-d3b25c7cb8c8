# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 6
  cacheKey: 8

"@discoveryjs/json-ext@npm:^0.5.0":
  version: 0.5.7
  resolution: "@discoveryjs/json-ext@npm:0.5.7"
  checksum: 2176d301cc258ea5c2324402997cf8134ebb212469c0d397591636cea8d3c02f2b3cf9fd58dcb748c7a0dade77ebdc1b10284fa63e608c033a1db52fddc69918
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.5
  resolution: "@jridgewell/gen-mapping@npm:0.3.5"
  dependencies:
    "@jridgewell/set-array": ^1.2.1
    "@jridgewell/sourcemap-codec": ^1.4.10
    "@jridgewell/trace-mapping": ^0.3.24
  checksum: ff7a1764ebd76a5e129c8890aa3e2f46045109dabde62b0b6c6a250152227647178ff2069ea234753a690d8f3c4ac8b5e7b267bbee272bffb7f3b0a370ab6e52
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 83b85f72c59d1c080b4cbec0fef84528963a1b5db34e4370fa4bd1e3ff64a0d80e0cee7369d11d73c704e0286fb2865b530acac7a871088fbe92b5edf1000870
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.2.1":
  version: 1.2.1
  resolution: "@jridgewell/set-array@npm:1.2.1"
  checksum: 832e513a85a588f8ed4f27d1279420d8547743cc37fcad5a5a76fc74bb895b013dfe614d0eed9cb860048e6546b798f8f2652020b4b2ba0561b05caa8c654b10
  languageName: node
  linkType: hard

"@jridgewell/source-map@npm:^0.3.3":
  version: 0.3.6
  resolution: "@jridgewell/source-map@npm:0.3.6"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.5
    "@jridgewell/trace-mapping": ^0.3.25
  checksum: c9dc7d899397df95e3c9ec287b93c0b56f8e4453cd20743e2b9c8e779b1949bc3cccf6c01bb302779e46560eb45f62ea38d19fedd25370d814734268450a9f30
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14":
  version: 1.4.15
  resolution: "@jridgewell/sourcemap-codec@npm:1.4.15"
  checksum: b881c7e503db3fc7f3c1f35a1dd2655a188cc51a3612d76efc8a6eb74728bef5606e6758ee77423e564092b4a518aba569bbb21c9bac5ab7a35b0c6ae7e344c8
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.20, @jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": ^3.1.0
    "@jridgewell/sourcemap-codec": ^1.4.14
  checksum: 9d3c40d225e139987b50c48988f8717a54a8c994d8a948ee42e1412e08988761d0754d7d10b803061cc3aebf35f92a5dbbab493bd0e1a9ef9e89a2130e83ba34
  languageName: node
  linkType: hard

"@jupyter/react-components@npm:^0.15.2":
  version: 0.15.3
  resolution: "@jupyter/react-components@npm:0.15.3"
  dependencies:
    "@jupyter/web-components": ^0.15.3
    "@microsoft/fast-react-wrapper": ^0.3.22
    react: ">=17.0.0 <19.0.0"
  checksum: 1a6b256314259c6465c4b6d958575710536b82234a7bf0fba3e889a07e1f19ff8ab321450be354359876f92c45dbcc9d21a840237ff4a619806d9de696f55496
  languageName: node
  linkType: hard

"@jupyter/web-components@npm:^0.15.2, @jupyter/web-components@npm:^0.15.3":
  version: 0.15.3
  resolution: "@jupyter/web-components@npm:0.15.3"
  dependencies:
    "@microsoft/fast-colors": ^5.3.1
    "@microsoft/fast-element": ^1.12.0
    "@microsoft/fast-foundation": ^2.49.4
    "@microsoft/fast-web-utilities": ^5.4.1
  checksum: a0980af934157bfdbdb6cc169c0816c1b2e57602d524c56bdcef746a4c25dfeb8f505150d83207c8695ed89b5486cf53d35a3382584d25ef64db666e4e16e45b
  languageName: node
  linkType: hard

"@jupyter/ydoc@npm:^1.1.1":
  version: 1.1.1
  resolution: "@jupyter/ydoc@npm:1.1.1"
  dependencies:
    "@jupyterlab/nbformat": ^3.0.0 || ^4.0.0-alpha.21 || ^4.0.0
    "@lumino/coreutils": ^1.11.0 || ^2.0.0
    "@lumino/disposable": ^1.10.0 || ^2.0.0
    "@lumino/signaling": ^1.10.0 || ^2.0.0
    y-protocols: ^1.0.5
    yjs: ^13.5.40
  checksum: a239b1dd57cfc9ba36c06ac5032a1b6388849ae01a1d0db0d45094f71fdadf4d473b4bf8becbef0cfcdc85cae505361fbec0822b02da5aa48e06b66f742dd7a0
  languageName: node
  linkType: hard

"@jupyterlab/apputils@npm:^4.2.5":
  version: 4.2.5
  resolution: "@jupyterlab/apputils@npm:4.2.5"
  dependencies:
    "@jupyterlab/coreutils": ^6.1.5
    "@jupyterlab/observables": ^5.1.5
    "@jupyterlab/rendermime-interfaces": ^3.9.5
    "@jupyterlab/services": ^7.1.5
    "@jupyterlab/settingregistry": ^4.1.5
    "@jupyterlab/statedb": ^4.1.5
    "@jupyterlab/statusbar": ^4.1.5
    "@jupyterlab/translation": ^4.1.5
    "@jupyterlab/ui-components": ^4.1.5
    "@lumino/algorithm": ^2.0.1
    "@lumino/commands": ^2.2.0
    "@lumino/coreutils": ^2.1.2
    "@lumino/disposable": ^2.1.2
    "@lumino/domutils": ^2.0.1
    "@lumino/messaging": ^2.0.1
    "@lumino/signaling": ^2.1.2
    "@lumino/virtualdom": ^2.0.1
    "@lumino/widgets": ^2.3.1
    "@types/react": ^18.0.26
    react: ^18.2.0
    sanitize-html: ~2.7.3
  checksum: e5652a14d1d7972bcff84cec13fc2849a6520f6e7cb82275eff37869afdb7aa856af88dad5621dfb967ea99733539488164d3b5f54885248a87adf4c86c2ce65
  languageName: node
  linkType: hard

"@jupyterlab/builder@npm:^4.0.0":
  version: 4.1.5
  resolution: "@jupyterlab/builder@npm:4.1.5"
  dependencies:
    "@lumino/algorithm": ^2.0.1
    "@lumino/application": ^2.3.0
    "@lumino/commands": ^2.2.0
    "@lumino/coreutils": ^2.1.2
    "@lumino/disposable": ^2.1.2
    "@lumino/domutils": ^2.0.1
    "@lumino/dragdrop": ^2.1.4
    "@lumino/messaging": ^2.0.1
    "@lumino/properties": ^2.0.1
    "@lumino/signaling": ^2.1.2
    "@lumino/virtualdom": ^2.0.1
    "@lumino/widgets": ^2.3.1
    ajv: ^8.12.0
    commander: ^9.4.1
    css-loader: ^6.7.1
    duplicate-package-checker-webpack-plugin: ^3.0.0
    fs-extra: ^10.1.0
    glob: ~7.1.6
    license-webpack-plugin: ^2.3.14
    mini-css-extract-plugin: ^2.7.0
    mini-svg-data-uri: ^1.4.4
    path-browserify: ^1.0.0
    process: ^0.11.10
    source-map-loader: ~1.0.2
    style-loader: ~3.3.1
    supports-color: ^7.2.0
    terser-webpack-plugin: ^5.3.7
    webpack: ^5.76.1
    webpack-cli: ^5.0.1
    webpack-merge: ^5.8.0
    worker-loader: ^3.0.2
  bin:
    build-labextension: lib/build-labextension.js
  checksum: f7618f4ab388337248018680456f886d106e096a425c406f743559f11ef21380b9fc1d578bc008f7336e38b050ebe945e45d26bb18e7f6c28b27e2ff72e8be0e
  languageName: node
  linkType: hard

"@jupyterlab/coreutils@npm:^6.1.5":
  version: 6.1.5
  resolution: "@jupyterlab/coreutils@npm:6.1.5"
  dependencies:
    "@lumino/coreutils": ^2.1.2
    "@lumino/disposable": ^2.1.2
    "@lumino/signaling": ^2.1.2
    minimist: ~1.2.0
    path-browserify: ^1.0.0
    url-parse: ~1.5.4
  checksum: b91c5a374f3c97d62e2442bb5f12cb79c6e440b5f6aa4d4ed6e492e8ca38836f7068106bb7029834a4e5de1947a9c44c342d23bedf9a4611aafca33629aed049
  languageName: node
  linkType: hard

"@jupyterlab/launcher@npm:^4.0.0":
  version: 4.1.5
  resolution: "@jupyterlab/launcher@npm:4.1.5"
  dependencies:
    "@jupyterlab/apputils": ^4.2.5
    "@jupyterlab/translation": ^4.1.5
    "@jupyterlab/ui-components": ^4.1.5
    "@lumino/algorithm": ^2.0.1
    "@lumino/commands": ^2.2.0
    "@lumino/coreutils": ^2.1.2
    "@lumino/disposable": ^2.1.2
    "@lumino/properties": ^2.0.1
    "@lumino/widgets": ^2.3.1
    react: ^18.2.0
  checksum: 78ffd573f18f21e954969e29e4d2002631b7ccee4f44dc32373e8fe64fdc0ba523aec6f535e636e0395953cc1013a22b1cc520ad75cfc3029c842335030785b1
  languageName: node
  linkType: hard

"@jupyterlab/nbformat@npm:^3.0.0 || ^4.0.0-alpha.21 || ^4.0.0, @jupyterlab/nbformat@npm:^4.1.5":
  version: 4.1.5
  resolution: "@jupyterlab/nbformat@npm:4.1.5"
  dependencies:
    "@lumino/coreutils": ^2.1.2
  checksum: d417d7eade40d389fea8593358b6455158cf3e67fa40c0c4c05c865852520acc466102109723c9cb16eecf95952617d79f7fe6be9da6ca3f601749bdecdfda97
  languageName: node
  linkType: hard

"@jupyterlab/observables@npm:^5.1.5":
  version: 5.1.5
  resolution: "@jupyterlab/observables@npm:5.1.5"
  dependencies:
    "@lumino/algorithm": ^2.0.1
    "@lumino/coreutils": ^2.1.2
    "@lumino/disposable": ^2.1.2
    "@lumino/messaging": ^2.0.1
    "@lumino/signaling": ^2.1.2
  checksum: 6d45de8a137c79566818ff56460366419b2603a06ab5d9cef4f0b311df3fd69c755b357ab3bd9c26ed56dec5a2247ef0cfc15cfa6e2e180aa46af7f96c6ab10c
  languageName: node
  linkType: hard

"@jupyterlab/rendermime-interfaces@npm:^3.9.5":
  version: 3.9.5
  resolution: "@jupyterlab/rendermime-interfaces@npm:3.9.5"
  dependencies:
    "@lumino/coreutils": ^1.11.0 || ^2.1.2
    "@lumino/widgets": ^1.37.2 || ^2.3.1
  checksum: 790c8d4d58213c02470599b2c69e8ccff8d3496750fc88403aafe4e7bc26bb262d40c9ed3fdd27fdfd77268b94e7ea4e178f73897dd42d9ab18cbe5a359d925c
  languageName: node
  linkType: hard

"@jupyterlab/services@npm:^7.1.5":
  version: 7.1.5
  resolution: "@jupyterlab/services@npm:7.1.5"
  dependencies:
    "@jupyter/ydoc": ^1.1.1
    "@jupyterlab/coreutils": ^6.1.5
    "@jupyterlab/nbformat": ^4.1.5
    "@jupyterlab/settingregistry": ^4.1.5
    "@jupyterlab/statedb": ^4.1.5
    "@lumino/coreutils": ^2.1.2
    "@lumino/disposable": ^2.1.2
    "@lumino/polling": ^2.1.2
    "@lumino/properties": ^2.0.1
    "@lumino/signaling": ^2.1.2
    ws: ^8.11.0
  checksum: f4b20ee62e5c3c7e0fa5942d3deb95329beb5a9ea6295403eefc0d5a723665379a09c58b21bc6a9fed7a69990570e5cfb66bc314e037a452b678fc4ec237dc55
  languageName: node
  linkType: hard

"@jupyterlab/settingregistry@npm:^4.1.5":
  version: 4.1.5
  resolution: "@jupyterlab/settingregistry@npm:4.1.5"
  dependencies:
    "@jupyterlab/nbformat": ^4.1.5
    "@jupyterlab/statedb": ^4.1.5
    "@lumino/commands": ^2.2.0
    "@lumino/coreutils": ^2.1.2
    "@lumino/disposable": ^2.1.2
    "@lumino/signaling": ^2.1.2
    "@rjsf/utils": ^5.13.4
    ajv: ^8.12.0
    json5: ^2.2.3
  peerDependencies:
    react: ">=16"
  checksum: 576d49cbbb4a18ba5f55230938b67c6dbc6819dfafb75ece2d9d030913e69768ddcb2616de4f7dbd3bcd8aa35e292aee90fe98b91e7dccdaae2610c64ec07f94
  languageName: node
  linkType: hard

"@jupyterlab/statedb@npm:^4.1.5":
  version: 4.1.5
  resolution: "@jupyterlab/statedb@npm:4.1.5"
  dependencies:
    "@lumino/commands": ^2.2.0
    "@lumino/coreutils": ^2.1.2
    "@lumino/disposable": ^2.1.2
    "@lumino/properties": ^2.0.1
    "@lumino/signaling": ^2.1.2
  checksum: e7f3ea9a5ebb04a602d93d1ddc9175a5b24a0f3814e99410ec3dba2dd3a86572ea61917d8a65e1b4b8c4ed25c8eaa814646a817a3b5d39b8a74a7b6cbb0071c1
  languageName: node
  linkType: hard

"@jupyterlab/statusbar@npm:^4.1.5":
  version: 4.1.5
  resolution: "@jupyterlab/statusbar@npm:4.1.5"
  dependencies:
    "@jupyterlab/ui-components": ^4.1.5
    "@lumino/algorithm": ^2.0.1
    "@lumino/coreutils": ^2.1.2
    "@lumino/disposable": ^2.1.2
    "@lumino/messaging": ^2.0.1
    "@lumino/signaling": ^2.1.2
    "@lumino/widgets": ^2.3.1
    react: ^18.2.0
  checksum: 402f3b80495c155f6c08447ca6ef348dbaae030cc6c20d11a7f4f365445f389dd71fefe9649296d59e8c698aa31347fb563b9a962e51b8712ed3bbe2cfd0ca37
  languageName: node
  linkType: hard

"@jupyterlab/translation@npm:^4.1.5":
  version: 4.1.5
  resolution: "@jupyterlab/translation@npm:4.1.5"
  dependencies:
    "@jupyterlab/coreutils": ^6.1.5
    "@jupyterlab/rendermime-interfaces": ^3.9.5
    "@jupyterlab/services": ^7.1.5
    "@jupyterlab/statedb": ^4.1.5
    "@lumino/coreutils": ^2.1.2
  checksum: f12e2f13048cd1628a9a03003401009972a3439a038314e2c7cdf19ab4c29fa02a0091475bdd1ddb7cb26e2175c401a86ab8664f54b99bb47962cbd595e6f643
  languageName: node
  linkType: hard

"@jupyterlab/ui-components@npm:^4.1.5":
  version: 4.1.5
  resolution: "@jupyterlab/ui-components@npm:4.1.5"
  dependencies:
    "@jupyter/react-components": ^0.15.2
    "@jupyter/web-components": ^0.15.2
    "@jupyterlab/coreutils": ^6.1.5
    "@jupyterlab/observables": ^5.1.5
    "@jupyterlab/rendermime-interfaces": ^3.9.5
    "@jupyterlab/translation": ^4.1.5
    "@lumino/algorithm": ^2.0.1
    "@lumino/commands": ^2.2.0
    "@lumino/coreutils": ^2.1.2
    "@lumino/disposable": ^2.1.2
    "@lumino/messaging": ^2.0.1
    "@lumino/polling": ^2.1.2
    "@lumino/properties": ^2.0.1
    "@lumino/signaling": ^2.1.2
    "@lumino/virtualdom": ^2.0.1
    "@lumino/widgets": ^2.3.1
    "@rjsf/core": ^5.13.4
    "@rjsf/utils": ^5.13.4
    react: ^18.2.0
    react-dom: ^18.2.0
    typestyle: ^2.0.4
  peerDependencies:
    react: ^18.2.0
  checksum: a50315549c03718b5e953bdb695757b1d39db293131dd5c2c26587612e0ed30ad208d1d65c86ddc153a241df2e01d3a9a162f0e4b5f86d2a20816260c9aefe67
  languageName: node
  linkType: hard

"@lumino/algorithm@npm:^2.0.1":
  version: 2.0.1
  resolution: "@lumino/algorithm@npm:2.0.1"
  checksum: cbf7fcf6ee6b785ea502cdfddc53d61f9d353dcb9659343511d5cd4b4030be2ff2ca4c08daec42f84417ab0318a3d9972a17319fa5231693e109ab112dcf8000
  languageName: node
  linkType: hard

"@lumino/application@npm:^2.3.0":
  version: 2.3.0
  resolution: "@lumino/application@npm:2.3.0"
  dependencies:
    "@lumino/commands": ^2.2.0
    "@lumino/coreutils": ^2.1.2
    "@lumino/widgets": ^2.3.1
  checksum: 9d1eb5bc972ed158bf219604a53bbac1262059bc5b0123d3e041974486b9cbb8288abeeec916f3b62f62d7c32e716cccf8b73e4832ae927e4f9dd4e4b0cd37ed
  languageName: node
  linkType: hard

"@lumino/collections@npm:^2.0.1":
  version: 2.0.1
  resolution: "@lumino/collections@npm:2.0.1"
  dependencies:
    "@lumino/algorithm": ^2.0.1
  checksum: 8a29b7973a388a33c5beda0819dcd2dc2aad51a8406dcfd4581b055a9f77a39dc5800f7a8b4ae3c0bb97ae7b56a7a869e2560ffb7a920a28e93b477ba05907d6
  languageName: node
  linkType: hard

"@lumino/commands@npm:^2.2.0":
  version: 2.2.0
  resolution: "@lumino/commands@npm:2.2.0"
  dependencies:
    "@lumino/algorithm": ^2.0.1
    "@lumino/coreutils": ^2.1.2
    "@lumino/disposable": ^2.1.2
    "@lumino/domutils": ^2.0.1
    "@lumino/keyboard": ^2.0.1
    "@lumino/signaling": ^2.1.2
    "@lumino/virtualdom": ^2.0.1
  checksum: 093e9715491e5cef24bc80665d64841417b400f2fa595f9b60832a3b6340c405c94a6aa276911944a2c46d79a6229f3cc087b73f50852bba25ece805abd0fae9
  languageName: node
  linkType: hard

"@lumino/coreutils@npm:^1.11.0 || ^2.0.0, @lumino/coreutils@npm:^1.11.0 || ^2.1.2, @lumino/coreutils@npm:^2.1.2":
  version: 2.1.2
  resolution: "@lumino/coreutils@npm:2.1.2"
  checksum: 7865317ac0676b448d108eb57ab5d8b2a17c101995c0f7a7106662d9fe6c859570104525f83ee3cda12ae2e326803372206d6f4c1f415a5b59e4158a7b81066f
  languageName: node
  linkType: hard

"@lumino/disposable@npm:^1.10.0 || ^2.0.0, @lumino/disposable@npm:^2.1.2":
  version: 2.1.2
  resolution: "@lumino/disposable@npm:2.1.2"
  dependencies:
    "@lumino/signaling": ^2.1.2
  checksum: ac2fb2bf18d0b2939fda454f3db248a0ff6e8a77b401e586d1caa9293b3318f808b93a117c9c3ac27cd17aab545aea83b49108d099b9b2f5503ae2a012fbc6e2
  languageName: node
  linkType: hard

"@lumino/domutils@npm:^2.0.1":
  version: 2.0.1
  resolution: "@lumino/domutils@npm:2.0.1"
  checksum: 61fa0ab226869dfbb763fc426790cf5a43b7d6f4cea1364c6dd56d61c44bff05eea188d33ff847449608ef58ed343161bee15c19b96f35410e4ee35815dc611a
  languageName: node
  linkType: hard

"@lumino/dragdrop@npm:^2.1.4":
  version: 2.1.4
  resolution: "@lumino/dragdrop@npm:2.1.4"
  dependencies:
    "@lumino/coreutils": ^2.1.2
    "@lumino/disposable": ^2.1.2
  checksum: 43d82484b13b38b612e7dfb424a840ed6a38d0db778af10655c4ba235c67b5b12db1683929b35a36ab2845f77466066dfd1ee25c1c273e8e175677eba9dc560d
  languageName: node
  linkType: hard

"@lumino/keyboard@npm:^2.0.1":
  version: 2.0.1
  resolution: "@lumino/keyboard@npm:2.0.1"
  checksum: cf33f13427a418efd7cc91061233321e860d5404f3d86397781028309bef86c8ad2d88276ffe335c1db0fe619bd9d1e60641c81f881696957a58703ee4652c3e
  languageName: node
  linkType: hard

"@lumino/messaging@npm:^2.0.1":
  version: 2.0.1
  resolution: "@lumino/messaging@npm:2.0.1"
  dependencies:
    "@lumino/algorithm": ^2.0.1
    "@lumino/collections": ^2.0.1
  checksum: 964c4651c374b17452b4252b7d71500b32d2ecd87c192fc5bcf5d3bd1070661d78d07edcac8eca7d1d6fd50aa25992505485e1296d6dd995691b8e349b652045
  languageName: node
  linkType: hard

"@lumino/polling@npm:^2.1.2":
  version: 2.1.2
  resolution: "@lumino/polling@npm:2.1.2"
  dependencies:
    "@lumino/coreutils": ^2.1.2
    "@lumino/disposable": ^2.1.2
    "@lumino/signaling": ^2.1.2
  checksum: fa9b401e6dbeb8f31d7e3ba485e8ef1e0c92b3f2da086239c0ed49931026f5d3528709193c93e031e35ac624fb4bbbfcdcbaa0e25eb797f36e2952e5cd91e9e3
  languageName: node
  linkType: hard

"@lumino/properties@npm:^2.0.1":
  version: 2.0.1
  resolution: "@lumino/properties@npm:2.0.1"
  checksum: c50173a935148cc4148fdaea119df1d323ee004ae16ab666800388d27e9730345629662d85f25591683329b39f0cdae60ee8c94e8943b4d0ef7d7370a38128d6
  languageName: node
  linkType: hard

"@lumino/signaling@npm:^1.10.0 || ^2.0.0, @lumino/signaling@npm:^2.1.2":
  version: 2.1.2
  resolution: "@lumino/signaling@npm:2.1.2"
  dependencies:
    "@lumino/algorithm": ^2.0.1
    "@lumino/coreutils": ^2.1.2
  checksum: ad7d7153db57980da899c43e412e6130316ef30b231a70250e7af49058db16cadb018c1417a2ea8083d83c48623cfe6b705fa82bf10216b1a8949aed9f4aca4e
  languageName: node
  linkType: hard

"@lumino/virtualdom@npm:^2.0.1":
  version: 2.0.1
  resolution: "@lumino/virtualdom@npm:2.0.1"
  dependencies:
    "@lumino/algorithm": ^2.0.1
  checksum: cf59b6f15b430e13e9e657b7a0619b9056cd9ea7b2a87f407391d071c501b77403c302b6a66dca510382045e75b2e3fe551630bb391f1c6b33678057d4bec164
  languageName: node
  linkType: hard

"@lumino/widgets@npm:^1.37.2 || ^2.3.1, @lumino/widgets@npm:^2.3.1":
  version: 2.3.1
  resolution: "@lumino/widgets@npm:2.3.1"
  dependencies:
    "@lumino/algorithm": ^2.0.1
    "@lumino/commands": ^2.2.0
    "@lumino/coreutils": ^2.1.2
    "@lumino/disposable": ^2.1.2
    "@lumino/domutils": ^2.0.1
    "@lumino/dragdrop": ^2.1.4
    "@lumino/keyboard": ^2.0.1
    "@lumino/messaging": ^2.0.1
    "@lumino/properties": ^2.0.1
    "@lumino/signaling": ^2.1.2
    "@lumino/virtualdom": ^2.0.1
  checksum: ba7b8f8839c1cd2a41dbda13281094eb6981a270cccf4f25a0cf83686dcc526a2d8044a20204317630bb7dd4a04d65361408c7623a921549c781afca84b91c67
  languageName: node
  linkType: hard

"@microsoft/fast-colors@npm:^5.3.1":
  version: 5.3.1
  resolution: "@microsoft/fast-colors@npm:5.3.1"
  checksum: ff87f402faadb4b5aeee3d27762566c11807f927cd4012b8bbc7f073ca68de0e2197f95330ff5dfd7038f4b4f0e2f51b11feb64c5d570f5c598d37850a5daf60
  languageName: node
  linkType: hard

"@microsoft/fast-element@npm:^1.12.0":
  version: 1.12.0
  resolution: "@microsoft/fast-element@npm:1.12.0"
  checksum: bbff4e9c83106d1d74f3eeedc87bf84832429e78fee59c6a4ae8164ee4f42667503f586896bea72341b4d2c76c244a3cb0d4fd0d5d3732755f00357714dd609e
  languageName: node
  linkType: hard

"@microsoft/fast-foundation@npm:^2.49.4, @microsoft/fast-foundation@npm:^2.49.5":
  version: 2.49.5
  resolution: "@microsoft/fast-foundation@npm:2.49.5"
  dependencies:
    "@microsoft/fast-element": ^1.12.0
    "@microsoft/fast-web-utilities": ^5.4.1
    tabbable: ^5.2.0
    tslib: ^1.13.0
  checksum: 8a4729e8193ee93f780dc88fac26561b42f2636e3f0a8e89bb1dfe256f50a01a21ed1d8e4d31ce40678807dc833e25f31ba735cb5d3c247b65219aeb2560c82c
  languageName: node
  linkType: hard

"@microsoft/fast-react-wrapper@npm:^0.3.22":
  version: 0.3.23
  resolution: "@microsoft/fast-react-wrapper@npm:0.3.23"
  dependencies:
    "@microsoft/fast-element": ^1.12.0
    "@microsoft/fast-foundation": ^2.49.5
  peerDependencies:
    react: ">=16.9.0"
  checksum: 45885e1868916d2aa9059e99c341c97da434331d9340a57128d4218081df68b5e1107031c608db9a550d6d1c3b010d516ed4f8dc5a8a2470058da6750dcd204a
  languageName: node
  linkType: hard

"@microsoft/fast-web-utilities@npm:^5.4.1":
  version: 5.4.1
  resolution: "@microsoft/fast-web-utilities@npm:5.4.1"
  dependencies:
    exenv-es6: ^1.1.1
  checksum: 303e87847f962944f474e3716c3eb305668243916ca9e0719e26bb9a32346144bc958d915c103776b3e552cea0f0f6233f839fad66adfdf96a8436b947288ca7
  languageName: node
  linkType: hard

"@rjsf/core@npm:^5.13.4":
  version: 5.17.1
  resolution: "@rjsf/core@npm:5.17.1"
  dependencies:
    lodash: ^4.17.21
    lodash-es: ^4.17.21
    markdown-to-jsx: ^7.4.1
    nanoid: ^3.3.7
    prop-types: ^15.8.1
  peerDependencies:
    "@rjsf/utils": ^5.16.x
    react: ^16.14.0 || >=17
  checksum: 2dead2886a4db152d259d3e85281c1fa5975eeac5f05c2840201ccc583ef1cf9d48c922cd404d515133e140eae7a8fca4aa63ccde0bcfe63d0b3fbe3cd621aed
  languageName: node
  linkType: hard

"@rjsf/utils@npm:^5.13.4":
  version: 5.17.1
  resolution: "@rjsf/utils@npm:5.17.1"
  dependencies:
    json-schema-merge-allof: ^0.8.1
    jsonpointer: ^5.0.1
    lodash: ^4.17.21
    lodash-es: ^4.17.21
    react-is: ^18.2.0
  peerDependencies:
    react: ^16.14.0 || >=17
  checksum: 83010de66b06f1046b023a0b7d0bf30b5f47b152893c3b12f1f42faa89e7c7d18b2f04fe2e9035e5f63454317f09e6d5753fc014d43b933c8023b71fc50c3acf
  languageName: node
  linkType: hard

"@types/eslint-scope@npm:^3.7.7":
  version: 3.7.7
  resolution: "@types/eslint-scope@npm:3.7.7"
  dependencies:
    "@types/eslint": "*"
    "@types/estree": "*"
  checksum: e2889a124aaab0b89af1bab5959847c5bec09809209255de0e63b9f54c629a94781daa04adb66bffcdd742f5e25a17614fb933965093c0eea64aacda4309380e
  languageName: node
  linkType: hard

"@types/eslint@npm:*":
  version: 8.56.6
  resolution: "@types/eslint@npm:8.56.6"
  dependencies:
    "@types/estree": "*"
    "@types/json-schema": "*"
  checksum: 960996940c8702c6e9bf221f2927f088d8f6463ad21ae1eb8260c62642ce48097a79a4277d99cb7cafde6939beadbd79610015fdd08b18679e565bcad5fcd36f
  languageName: node
  linkType: hard

"@types/estree@npm:*":
  version: 1.0.5
  resolution: "@types/estree@npm:1.0.5"
  checksum: dd8b5bed28e6213b7acd0fb665a84e693554d850b0df423ac8076cc3ad5823a6bc26b0251d080bdc545af83179ede51dd3f6fa78cad2c46ed1f29624ddf3e41a
  languageName: node
  linkType: hard

"@types/estree@npm:^1.0.6":
  version: 1.0.7
  resolution: "@types/estree@npm:1.0.7"
  checksum: d9312b7075bdd08f3c9e1bb477102f5458aaa42a8eec31a169481ce314ca99ac716645cff4fca81ea65a2294b0276a0de63159d1baca0f8e7b5050a92de950ad
  languageName: node
  linkType: hard

"@types/json-schema@npm:*, @types/json-schema@npm:^7.0.5, @types/json-schema@npm:^7.0.8, @types/json-schema@npm:^7.0.9":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 97ed0cb44d4070aecea772b7b2e2ed971e10c81ec87dd4ecc160322ffa55ff330dace1793489540e3e318d90942064bb697cc0f8989391797792d919737b3b98
  languageName: node
  linkType: hard

"@types/node@npm:*":
  version: 20.11.30
  resolution: "@types/node@npm:20.11.30"
  dependencies:
    undici-types: ~5.26.4
  checksum: 7597767aa3e44b0f1bf62efa522dd17741135f283c11de6a20ead8bb7016fb4999cc30adcd8f2bb29ebb216906c92894346ccd187de170927dc1e212d2c07c81
  languageName: node
  linkType: hard

"@types/prop-types@npm:*":
  version: 15.7.11
  resolution: "@types/prop-types@npm:15.7.11"
  checksum: 7519ff11d06fbf6b275029fe03fff9ec377b4cb6e864cac34d87d7146c7f5a7560fd164bdc1d2dbe00b60c43713631251af1fd3d34d46c69cd354602bc0c7c54
  languageName: node
  linkType: hard

"@types/react@npm:^18.0.26":
  version: 18.2.67
  resolution: "@types/react@npm:18.2.67"
  dependencies:
    "@types/prop-types": "*"
    "@types/scheduler": "*"
    csstype: ^3.0.2
  checksum: 9e48122b0e8d0ae20601f613aca32289eeb94d55c58ff03a7dc532d80ed289a1ba9715f14b773bd926ccdd63e503717f14264143ef531dc370063abe09dee0b2
  languageName: node
  linkType: hard

"@types/scheduler@npm:*":
  version: 0.16.8
  resolution: "@types/scheduler@npm:0.16.8"
  checksum: 6c091b096daa490093bf30dd7947cd28e5b2cd612ec93448432b33f724b162587fed9309a0acc104d97b69b1d49a0f3fc755a62282054d62975d53d7fd13472d
  languageName: node
  linkType: hard

"@types/source-list-map@npm:*":
  version: 0.1.6
  resolution: "@types/source-list-map@npm:0.1.6"
  checksum: 9cd294c121f1562062de5d241fe4d10780b1131b01c57434845fe50968e9dcf67ede444591c2b1ad6d3f9b6bc646ac02cc8f51a3577c795f9c64cf4573dcc6b1
  languageName: node
  linkType: hard

"@types/webpack-sources@npm:^0.1.5":
  version: 0.1.12
  resolution: "@types/webpack-sources@npm:0.1.12"
  dependencies:
    "@types/node": "*"
    "@types/source-list-map": "*"
    source-map: ^0.6.1
  checksum: 75342659a9889478969f7bb7360b998aa084ba11ab523c172ded6a807dac43ab2a9e1212078ef8bbf0f33e4fadd2c8a91b75d38184d8030d96a32fe819c9bb57
  languageName: node
  linkType: hard

"@webassemblyjs/ast@npm:1.14.1, @webassemblyjs/ast@npm:^1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/ast@npm:1.14.1"
  dependencies:
    "@webassemblyjs/helper-numbers": 1.13.2
    "@webassemblyjs/helper-wasm-bytecode": 1.13.2
  checksum: f9154ad9ea14f6f2374ebe918c221fd69a4d4514126a1acc6fa4966e8d27ab28cb550a5e6880032cf620e19640578658a7e5a55bd2aad1e3db4e9d598b8f2099
  languageName: node
  linkType: hard

"@webassemblyjs/floating-point-hex-parser@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/floating-point-hex-parser@npm:1.13.2"
  checksum: e866ec8433f4a70baa511df5e8f2ebcd6c24f4e2cc6274c7c5aabe2bcce3459ea4680e0f35d450e1f3602acf3913b6b8e4f15069c8cfd34ae8609fb9a7d01795
  languageName: node
  linkType: hard

"@webassemblyjs/helper-api-error@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/helper-api-error@npm:1.13.2"
  checksum: 48b5df7fd3095bb252f59a139fe2cbd999a62ac9b488123e9a0da3906ad8a2f2da7b2eb21d328c01a90da987380928706395c2897d1f3ed9e2125b6d75a920d0
  languageName: node
  linkType: hard

"@webassemblyjs/helper-buffer@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/helper-buffer@npm:1.14.1"
  checksum: b611e981dfd6a797c3d8fc3a772de29a6e55033737c2c09c31bb66c613bdbb2d25f915df1dee62a602c6acc057ca71128432fa8c3e22a893e1219dc454f14ede
  languageName: node
  linkType: hard

"@webassemblyjs/helper-numbers@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/helper-numbers@npm:1.13.2"
  dependencies:
    "@webassemblyjs/floating-point-hex-parser": 1.13.2
    "@webassemblyjs/helper-api-error": 1.13.2
    "@xtuc/long": 4.2.2
  checksum: 49e2c9bf9b66997e480f6b44d80f895b3cde4de52ac135921d28e144565edca6903a519f627f4089b5509de1d7f9e5023f0e1a94ff78a36c9e2eb30e7c18ffd2
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-bytecode@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/helper-wasm-bytecode@npm:1.13.2"
  checksum: 8e059e1c1f0294f4fc3df8e4eaff3c5ef6e2e1358f34ebc118eaf5070ed59e56ed7fc92b28be734ebde17c8d662d5d27e06ade686c282445135da083ae11c128
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-section@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/helper-wasm-section@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": 1.14.1
    "@webassemblyjs/helper-buffer": 1.14.1
    "@webassemblyjs/helper-wasm-bytecode": 1.13.2
    "@webassemblyjs/wasm-gen": 1.14.1
  checksum: 0a08d454a63192cd66abf91b6f060ac4b466cef341262246e9dcc828dd4c8536195dea9b46a1244b1eac65b59b8b502164a771a190052a92ff0a0a2ded0f8f53
  languageName: node
  linkType: hard

"@webassemblyjs/ieee754@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/ieee754@npm:1.13.2"
  dependencies:
    "@xtuc/ieee754": ^1.2.0
  checksum: d7e3520baa37a7309fa7db4d73d69fb869878853b1ebd4b168821bd03fcc4c0e1669c06231315b0039035d9a7a462e53de3ad982da4a426a4b0743b5888e8673
  languageName: node
  linkType: hard

"@webassemblyjs/leb128@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/leb128@npm:1.13.2"
  dependencies:
    "@xtuc/long": 4.2.2
  checksum: 64083507f7cff477a6d71a9e325d95665cea78ec8df99ca7c050e1cfbe300fbcf0842ca3dcf3b4fa55028350135588a4f879398d3dd2b6a8de9913ce7faf5333
  languageName: node
  linkType: hard

"@webassemblyjs/utf8@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/utf8@npm:1.13.2"
  checksum: 95ec6052f30eefa8d50c9b2a3394d08b17d53a4aa52821451d41d774c126fa8f39b988fbf5bff56da86852a87c16d676e576775a4071e5e5ccf020cc85a4b281
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-edit@npm:^1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wasm-edit@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": 1.14.1
    "@webassemblyjs/helper-buffer": 1.14.1
    "@webassemblyjs/helper-wasm-bytecode": 1.13.2
    "@webassemblyjs/helper-wasm-section": 1.14.1
    "@webassemblyjs/wasm-gen": 1.14.1
    "@webassemblyjs/wasm-opt": 1.14.1
    "@webassemblyjs/wasm-parser": 1.14.1
    "@webassemblyjs/wast-printer": 1.14.1
  checksum: 9341c3146bb1b7863f03d6050c2a66990f20384ca137388047bbe1feffacb599e94fca7b7c18287d17e2449ffb4005fdc7f41f674a6975af9ad8522756f8ffff
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-gen@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wasm-gen@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": 1.14.1
    "@webassemblyjs/helper-wasm-bytecode": 1.13.2
    "@webassemblyjs/ieee754": 1.13.2
    "@webassemblyjs/leb128": 1.13.2
    "@webassemblyjs/utf8": 1.13.2
  checksum: 401b12bec7431c4fc29d9414bbe40d3c6dc5be04d25a116657c42329f5481f0129f3b5834c293f26f0e42681ceac9157bf078ce9bdb6a7f78037c650373f98b2
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-opt@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wasm-opt@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": 1.14.1
    "@webassemblyjs/helper-buffer": 1.14.1
    "@webassemblyjs/wasm-gen": 1.14.1
    "@webassemblyjs/wasm-parser": 1.14.1
  checksum: 60c697a9e9129d8d23573856df0791ba33cea4a3bc2339044cae73128c0983802e5e50a42157b990eeafe1237eb8e7653db6de5f02b54a0ae7b81b02dcdf2ae9
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-parser@npm:1.14.1, @webassemblyjs/wasm-parser@npm:^1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wasm-parser@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": 1.14.1
    "@webassemblyjs/helper-api-error": 1.13.2
    "@webassemblyjs/helper-wasm-bytecode": 1.13.2
    "@webassemblyjs/ieee754": 1.13.2
    "@webassemblyjs/leb128": 1.13.2
    "@webassemblyjs/utf8": 1.13.2
  checksum: 93f1fe2676da465b4e824419d9812a3d7218de4c3addd4e916c04bc86055fa134416c1b67e4b7cbde8d728c0dce2721d06cc0bfe7a7db7c093a0898009937405
  languageName: node
  linkType: hard

"@webassemblyjs/wast-printer@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wast-printer@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": 1.14.1
    "@xtuc/long": 4.2.2
  checksum: 517881a0554debe6945de719d100b2d8883a2d24ddf47552cdeda866341e2bb153cd824a864bc7e2a61190a4b66b18f9899907e0074e9e820d2912ac0789ea60
  languageName: node
  linkType: hard

"@webpack-cli/configtest@npm:^2.1.1":
  version: 2.1.1
  resolution: "@webpack-cli/configtest@npm:2.1.1"
  peerDependencies:
    webpack: 5.x.x
    webpack-cli: 5.x.x
  checksum: 9f9f9145c2d05471fc83d426db1df85cf49f329836b0c4b9f46b6948bed4b013464c00622b136d2a0a26993ce2306976682592245b08ee717500b1db45009a72
  languageName: node
  linkType: hard

"@webpack-cli/info@npm:^2.0.2":
  version: 2.0.2
  resolution: "@webpack-cli/info@npm:2.0.2"
  peerDependencies:
    webpack: 5.x.x
    webpack-cli: 5.x.x
  checksum: 8f9a178afca5c82e113aed1efa552d64ee5ae4fdff63fe747c096a981ec74f18a5d07bd6e89bbe6715c3e57d96eea024a410e58977169489fe1df044c10dd94e
  languageName: node
  linkType: hard

"@webpack-cli/serve@npm:^2.0.5":
  version: 2.0.5
  resolution: "@webpack-cli/serve@npm:2.0.5"
  peerDependencies:
    webpack: 5.x.x
    webpack-cli: 5.x.x
  peerDependenciesMeta:
    webpack-dev-server:
      optional: true
  checksum: 75f0e54681796d567a71ac3e2781d2901a8d8cf1cdfc82f261034dddac59a8343e8c3bc5e32b4bb9d6766759ba49fb29a5cd86ef1701d79c506fe886bb63ac75
  languageName: node
  linkType: hard

"@xtuc/ieee754@npm:^1.2.0":
  version: 1.2.0
  resolution: "@xtuc/ieee754@npm:1.2.0"
  checksum: ac56d4ca6e17790f1b1677f978c0c6808b1900a5b138885d3da21732f62e30e8f0d9120fcf8f6edfff5100ca902b46f8dd7c1e3f903728634523981e80e2885a
  languageName: node
  linkType: hard

"@xtuc/long@npm:4.2.2":
  version: 4.2.2
  resolution: "@xtuc/long@npm:4.2.2"
  checksum: 8ed0d477ce3bc9c6fe2bf6a6a2cc316bb9c4127c5a7827bae947fa8ec34c7092395c5a283cc300c05b5fa01cbbfa1f938f410a7bf75db7c7846fea41949989ec
  languageName: node
  linkType: hard

"abab@npm:^2.0.3":
  version: 2.0.6
  resolution: "abab@npm:2.0.6"
  checksum: 6ffc1af4ff315066c62600123990d87551ceb0aafa01e6539da77b0f5987ac7019466780bf480f1787576d4385e3690c81ccc37cfda12819bf510b8ab47e5a3e
  languageName: node
  linkType: hard

"acorn@npm:^8.14.0":
  version: 8.14.1
  resolution: "acorn@npm:8.14.1"
  bin:
    acorn: bin/acorn
  checksum: 260d9bb6017a1b6e42d31364687f0258f78eb20210b36ef2baad38fd619d78d4e95ff7dde9b3dbe0d81f137f79a8d651a845363a26e6985997f7b71145dc5e94
  languageName: node
  linkType: hard

"acorn@npm:^8.8.2":
  version: 8.11.3
  resolution: "acorn@npm:8.11.3"
  bin:
    acorn: bin/acorn
  checksum: 76d8e7d559512566b43ab4aadc374f11f563f0a9e21626dd59cb2888444e9445923ae9f3699972767f18af61df89cd89f5eaaf772d1327b055b45cb829b4a88c
  languageName: node
  linkType: hard

"ajv-formats@npm:^2.1.1":
  version: 2.1.1
  resolution: "ajv-formats@npm:2.1.1"
  dependencies:
    ajv: ^8.0.0
  peerDependencies:
    ajv: ^8.0.0
  peerDependenciesMeta:
    ajv:
      optional: true
  checksum: 4a287d937f1ebaad4683249a4c40c0fa3beed30d9ddc0adba04859026a622da0d317851316ea64b3680dc60f5c3c708105ddd5d5db8fe595d9d0207fd19f90b7
  languageName: node
  linkType: hard

"ajv-keywords@npm:^3.5.2":
  version: 3.5.2
  resolution: "ajv-keywords@npm:3.5.2"
  peerDependencies:
    ajv: ^6.9.1
  checksum: 7dc5e5931677a680589050f79dcbe1fefbb8fea38a955af03724229139175b433c63c68f7ae5f86cf8f65d55eb7c25f75a046723e2e58296707617ca690feae9
  languageName: node
  linkType: hard

"ajv-keywords@npm:^5.1.0":
  version: 5.1.0
  resolution: "ajv-keywords@npm:5.1.0"
  dependencies:
    fast-deep-equal: ^3.1.3
  peerDependencies:
    ajv: ^8.8.2
  checksum: c35193940b853119242c6757787f09ecf89a2c19bcd36d03ed1a615e710d19d450cb448bfda407b939aba54b002368c8bff30529cc50a0536a8e10bcce300421
  languageName: node
  linkType: hard

"ajv@npm:^6.12.4, ajv@npm:^6.12.5":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: ^3.1.1
    fast-json-stable-stringify: ^2.0.0
    json-schema-traverse: ^0.4.1
    uri-js: ^4.2.2
  checksum: 874972efe5c4202ab0a68379481fbd3d1b5d0a7bd6d3cc21d40d3536ebff3352a2a1fabb632d4fd2cc7fe4cbdcd5ed6782084c9bbf7f32a1536d18f9da5007d4
  languageName: node
  linkType: hard

"ajv@npm:^8.0.0, ajv@npm:^8.12.0, ajv@npm:^8.9.0":
  version: 8.12.0
  resolution: "ajv@npm:8.12.0"
  dependencies:
    fast-deep-equal: ^3.1.1
    json-schema-traverse: ^1.0.0
    require-from-string: ^2.0.2
    uri-js: ^4.2.2
  checksum: 4dc13714e316e67537c8b31bc063f99a1d9d9a497eb4bbd55191ac0dcd5e4985bbb71570352ad6f1e76684fb6d790928f96ba3b2d4fd6e10024be9612fe3f001
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: ^1.9.0
  checksum: d85ade01c10e5dd77b6c89f34ed7531da5830d2cb5882c645f330079975b716438cd7ebb81d0d6e6b4f9c577f19ae41ab55f07f19786b02f9dfd9e0377395665
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"big.js@npm:^5.2.2":
  version: 5.2.2
  resolution: "big.js@npm:5.2.2"
  checksum: b89b6e8419b097a8fb4ed2399a1931a68c612bce3cfd5ca8c214b2d017531191070f990598de2fc6f3f993d91c0f08aa82697717f6b3b8732c9731866d233c9e
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: ^1.0.0
    concat-map: 0.0.1
  checksum: faf34a7bb0c3fcf4b59c7808bc5d2a96a40988addf2e7e09dfbb67a2251800e0d14cd2bfc1aa79174f2f5095c54ff27f46fb1289fe2d77dac755b5eb3434cc07
  languageName: node
  linkType: hard

"browserslist@npm:^4.24.0":
  version: 4.24.4
  resolution: "browserslist@npm:4.24.4"
  dependencies:
    caniuse-lite: ^1.0.30001688
    electron-to-chromium: ^1.5.73
    node-releases: ^2.0.19
    update-browserslist-db: ^1.1.1
  bin:
    browserslist: cli.js
  checksum: 64074bf6cf0a9ae3094d753270e3eae9cf925149db45d646f0bc67bacc2e46d7ded64a4e835b95f5fdcf0350f63a83c3755b32f80831f643a47f0886deb8a065
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 0448524a562b37d4d7ed9efd91685a5b77a50672c556ea254ac9a6d30e3403a517d8981f10e565db24e8339413b43c97ca2951f10e399c6125a0d8911f5679bb
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001688":
  version: 1.0.30001715
  resolution: "caniuse-lite@npm:1.0.30001715"
  checksum: c8371dceca0177518e43de537c74a01e64428ea65250d597c13472cf8277ffbc800c9a729ff0e7d271c8445ae90976ba64a170232b4498aee9552d993287a4c4
  languageName: node
  linkType: hard

"chalk@npm:^2.3.0":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: ^3.2.1
    escape-string-regexp: ^1.0.5
    supports-color: ^5.3.0
  checksum: ec3661d38fe77f681200f878edbd9448821924e0f93a9cefc0e26a33b145f1027a2084bf19967160d11e1f03bfe4eaffcabf5493b89098b2782c3fe0b03d80c2
  languageName: node
  linkType: hard

"chrome-trace-event@npm:^1.0.2":
  version: 1.0.3
  resolution: "chrome-trace-event@npm:1.0.3"
  checksum: cb8b1fc7e881aaef973bd0c4a43cd353c2ad8323fb471a041e64f7c2dd849cde4aad15f8b753331a32dda45c973f032c8a03b8177fc85d60eaa75e91e08bfb97
  languageName: node
  linkType: hard

"clone-deep@npm:^4.0.1":
  version: 4.0.1
  resolution: "clone-deep@npm:4.0.1"
  dependencies:
    is-plain-object: ^2.0.4
    kind-of: ^6.0.2
    shallow-clone: ^3.0.0
  checksum: 770f912fe4e6f21873c8e8fbb1e99134db3b93da32df271d00589ea4a29dbe83a9808a322c93f3bcaf8584b8b4fa6fc269fc8032efbaa6728e0c9886c74467d2
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: 1.1.3
  checksum: fd7a64a17cde98fb923b1dd05c5f2e6f7aefda1b60d67e8d449f9328b4e53b228a428fd38bfeaeb2db2ff6b6503a776a996150b80cdf224062af08a5c8a3a203
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 09c5d3e33d2105850153b14466501f2bfb30324a2f76568a408763a3b7433b0e50e5b4ab1947868e65cb101bb7cb75029553f2c333b6d4b8138a73fcc133d69d
  languageName: node
  linkType: hard

"colorette@npm:^2.0.14":
  version: 2.0.20
  resolution: "colorette@npm:2.0.20"
  checksum: 0c016fea2b91b733eb9f4bcdb580018f52c0bc0979443dad930e5037a968237ac53d9beb98e218d2e9235834f8eebce7f8e080422d6194e957454255bde71d3d
  languageName: node
  linkType: hard

"commander@npm:^10.0.1":
  version: 10.0.1
  resolution: "commander@npm:10.0.1"
  checksum: 436901d64a818295803c1996cd856621a74f30b9f9e28a588e726b2b1670665bccd7c1a77007ebf328729f0139838a88a19265858a0fa7a8728c4656796db948
  languageName: node
  linkType: hard

"commander@npm:^2.20.0":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: ab8c07884e42c3a8dbc5dd9592c606176c7eb5c1ca5ff274bcf907039b2c41de3626f684ea75ccf4d361ba004bbaff1f577d5384c155f3871e456bdf27becf9e
  languageName: node
  linkType: hard

"commander@npm:^9.4.1":
  version: 9.5.0
  resolution: "commander@npm:9.5.0"
  checksum: c7a3e27aa59e913b54a1bafd366b88650bc41d6651f0cbe258d4ff09d43d6a7394232a4dadd0bf518b3e696fdf595db1028a0d82c785b88bd61f8a440cecfade
  languageName: node
  linkType: hard

"compute-gcd@npm:^1.2.1":
  version: 1.2.1
  resolution: "compute-gcd@npm:1.2.1"
  dependencies:
    validate.io-array: ^1.0.3
    validate.io-function: ^1.0.2
    validate.io-integer-array: ^1.0.0
  checksum: 51cf33b75f7c8db5142fcb99a9d84a40260993fed8e02a7ab443834186c3ab99b3fd20b30ad9075a6a9d959d69df6da74dd3be8a59c78d9f2fe780ebda8242e1
  languageName: node
  linkType: hard

"compute-lcm@npm:^1.1.2":
  version: 1.1.2
  resolution: "compute-lcm@npm:1.1.2"
  dependencies:
    compute-gcd: ^1.2.1
    validate.io-array: ^1.0.3
    validate.io-function: ^1.0.2
    validate.io-integer-array: ^1.0.0
  checksum: d499ab57dcb48e8d0fd233b99844a06d1cc56115602c920c586e998ebba60293731f5b6976e8a1e83ae6cbfe86716f62d9432e8d94913fed8bd8352f447dc917
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 902a9f5d8967a3e2faf138d5cb784b9979bad2e6db5357c5b21c568df4ebe62bcb15108af1b2253744844eb964fc023fbd9afbbbb6ddd0bcc204c6fb5b7bf3af
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.3":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: ^3.1.0
    shebang-command: ^2.0.0
    which: ^2.0.1
  checksum: 8d306efacaf6f3f60e0224c287664093fa9185680b2d195852ba9a863f85d02dcc737094c6e512175f8ee0161f9b87c73c6826034c2422e39de7d6569cf4503b
  languageName: node
  linkType: hard

"css-loader@npm:^6.7.1":
  version: 6.10.0
  resolution: "css-loader@npm:6.10.0"
  dependencies:
    icss-utils: ^5.1.0
    postcss: ^8.4.33
    postcss-modules-extract-imports: ^3.0.0
    postcss-modules-local-by-default: ^4.0.4
    postcss-modules-scope: ^3.1.1
    postcss-modules-values: ^4.0.0
    postcss-value-parser: ^4.2.0
    semver: ^7.5.4
  peerDependencies:
    "@rspack/core": 0.x || 1.x
    webpack: ^5.0.0
  peerDependenciesMeta:
    "@rspack/core":
      optional: true
    webpack:
      optional: true
  checksum: ee3d62b5f7e4eb24281a22506431e920d07a45bd6ea627731ce583f3c6a846ab8b8b703bace599b9b35256b9e762f9f326d969abb72b69c7e6055eacf39074fd
  languageName: node
  linkType: hard

"cssesc@npm:^3.0.0":
  version: 3.0.0
  resolution: "cssesc@npm:3.0.0"
  bin:
    cssesc: bin/cssesc
  checksum: f8c4ababffbc5e2ddf2fa9957dda1ee4af6048e22aeda1869d0d00843223c1b13ad3f5d88b51caa46c994225eacb636b764eb807a8883e2fb6f99b4f4e8c48b2
  languageName: node
  linkType: hard

"csstype@npm:3.0.10":
  version: 3.0.10
  resolution: "csstype@npm:3.0.10"
  checksum: 20a8fa324f2b33ddf94aa7507d1b6ab3daa6f3cc308888dc50126585d7952f2471de69b2dbe0635d1fdc31223fef8e070842691877e725caf456e2378685a631
  languageName: node
  linkType: hard

"csstype@npm:^3.0.2":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 8db785cc92d259102725b3c694ec0c823f5619a84741b5c7991b8ad135dfaa66093038a1cc63e03361a6cd28d122be48f2106ae72334e067dd619a51f49eddf7
  languageName: node
  linkType: hard

"data-urls@npm:^2.0.0":
  version: 2.0.0
  resolution: "data-urls@npm:2.0.0"
  dependencies:
    abab: ^2.0.3
    whatwg-mimetype: ^2.3.0
    whatwg-url: ^8.0.0
  checksum: 97caf828aac25e25e04ba6869db0f99c75e6859bb5b424ada28d3e7841941ebf08ddff3c1b1bb4585986bd507a5d54c2a716853ea6cb98af877400e637393e71
  languageName: node
  linkType: hard

"deepmerge@npm:^4.2.2":
  version: 4.3.1
  resolution: "deepmerge@npm:4.3.1"
  checksum: 2024c6a980a1b7128084170c4cf56b0fd58a63f2da1660dcfe977415f27b17dbe5888668b59d0b063753f3220719d5e400b7f113609489c90160bb9a5518d052
  languageName: node
  linkType: hard

"dom-serializer@npm:^1.0.1":
  version: 1.4.1
  resolution: "dom-serializer@npm:1.4.1"
  dependencies:
    domelementtype: ^2.0.1
    domhandler: ^4.2.0
    entities: ^2.0.0
  checksum: fbb0b01f87a8a2d18e6e5a388ad0f7ec4a5c05c06d219377da1abc7bb0f674d804f4a8a94e3f71ff15f6cb7dcfc75704a54b261db672b9b3ab03da6b758b0b22
  languageName: node
  linkType: hard

"domelementtype@npm:^2.0.1, domelementtype@npm:^2.2.0":
  version: 2.3.0
  resolution: "domelementtype@npm:2.3.0"
  checksum: ee837a318ff702622f383409d1f5b25dd1024b692ef64d3096ff702e26339f8e345820f29a68bcdcea8cfee3531776b3382651232fbeae95612d6f0a75efb4f6
  languageName: node
  linkType: hard

"domhandler@npm:^4.0.0, domhandler@npm:^4.2.0":
  version: 4.3.1
  resolution: "domhandler@npm:4.3.1"
  dependencies:
    domelementtype: ^2.2.0
  checksum: 4c665ceed016e1911bf7d1dadc09dc888090b64dee7851cccd2fcf5442747ec39c647bb1cb8c8919f8bbdd0f0c625a6bafeeed4b2d656bbecdbae893f43ffaaa
  languageName: node
  linkType: hard

"domutils@npm:^2.5.2":
  version: 2.8.0
  resolution: "domutils@npm:2.8.0"
  dependencies:
    dom-serializer: ^1.0.1
    domelementtype: ^2.2.0
    domhandler: ^4.2.0
  checksum: abf7434315283e9aadc2a24bac0e00eab07ae4313b40cc239f89d84d7315ebdfd2fb1b5bf750a96bc1b4403d7237c7b2ebf60459be394d625ead4ca89b934391
  languageName: node
  linkType: hard

"duplicate-package-checker-webpack-plugin@npm:^3.0.0":
  version: 3.0.0
  resolution: "duplicate-package-checker-webpack-plugin@npm:3.0.0"
  dependencies:
    chalk: ^2.3.0
    find-root: ^1.0.0
    lodash: ^4.17.4
    semver: ^5.4.1
  checksum: d77be45cb72d79a429c64d8f8f7603fea681d182fb795459a3d4afa608faad9a923378a7e80c6855f465263e1983140b6fc3682bd0213228b8cd7906ab4b934d
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.73":
  version: 1.5.140
  resolution: "electron-to-chromium@npm:1.5.140"
  checksum: 42897acbc76102b164b2e691f41d271897ddcc0a34e401413a7b1f36f44eddc36d46206d1fdd846c4fde04e5341117d2519e910c0bbae75d42970e73dd3e1db8
  languageName: node
  linkType: hard

"emojis-list@npm:^3.0.0":
  version: 3.0.0
  resolution: "emojis-list@npm:3.0.0"
  checksum: ddaaa02542e1e9436c03970eeed445f4ed29a5337dfba0fe0c38dfdd2af5da2429c2a0821304e8a8d1cadf27fdd5b22ff793571fa803ae16852a6975c65e8e70
  languageName: node
  linkType: hard

"enhanced-resolve@npm:^5.17.1":
  version: 5.18.1
  resolution: "enhanced-resolve@npm:5.18.1"
  dependencies:
    graceful-fs: ^4.2.4
    tapable: ^2.2.0
  checksum: de5bea7debe3576e78173bcc409c4aee7fcb56580c602d5c47c533b92952e55d7da3d9f53b864846ba62c8bd3efb0f9ecfe5f865e57de2f3e9b6e5cda03b4e7e
  languageName: node
  linkType: hard

"entities@npm:^2.0.0":
  version: 2.2.0
  resolution: "entities@npm:2.2.0"
  checksum: 19010dacaf0912c895ea262b4f6128574f9ccf8d4b3b65c7e8334ad0079b3706376360e28d8843ff50a78aabcb8f08f0a32dbfacdc77e47ed77ca08b713669b3
  languageName: node
  linkType: hard

"envinfo@npm:^7.7.3":
  version: 7.11.1
  resolution: "envinfo@npm:7.11.1"
  bin:
    envinfo: dist/cli.js
  checksum: f3d38ab6bc62388466e86e2f5665f90f238ca349c81bb36b311d908cb5ca96650569b43b308c9dcb6725a222693f6c43a704794e74a68fb445ec5575a90ca05e
  languageName: node
  linkType: hard

"es-module-lexer@npm:^1.2.1":
  version: 1.4.2
  resolution: "es-module-lexer@npm:1.4.2"
  checksum: f4cfb9e1227f63c786d1c861a086cad477d2b9b29128b343d20e34ae775341a62f62cea0119976a1db58908c99f50a469ef9f3ec0529de012c6d780b41456912
  languageName: node
  linkType: hard

"escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 47b029c83de01b0d17ad99ed766347b974b0d628e848de404018f3abee728e987da0d2d370ad4574aa3d5b5bfc368754fd085d69a30f8e75903486ec4b5b709e
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 6092fda75c63b110c706b6a9bfde8a612ad595b628f0bd2147eea1d3406723020810e591effc7db1da91d80a71a737a313567c5abb3813e8d9c71f4aa595b410
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"eslint-scope@npm:5.1.1":
  version: 5.1.1
  resolution: "eslint-scope@npm:5.1.1"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^4.1.1
  checksum: 47e4b6a3f0cc29c7feedee6c67b225a2da7e155802c6ea13bbef4ac6b9e10c66cd2dcb987867ef176292bf4e64eccc680a49e35e9e9c669f4a02bac17e86abdb
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: ^5.2.0
  checksum: ebc17b1a33c51cef46fdc28b958994b1dc43cd2e86237515cbc3b4e5d2be6a811b2315d0a1a4d9d340b6d2308b15322f5c8291059521cc5f4802f65e7ec32837
  languageName: node
  linkType: hard

"estraverse@npm:^4.1.1":
  version: 4.3.0
  resolution: "estraverse@npm:4.3.0"
  checksum: a6299491f9940bb246124a8d44b7b7a413a8336f5436f9837aaa9330209bd9ee8af7e91a654a3545aee9c54b3308e78ee360cef1d777d37cfef77d2fa33b5827
  languageName: node
  linkType: hard

"estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 072780882dc8416ad144f8fe199628d2b3e7bbc9989d9ed43795d2c90309a2047e6bc5979d7e2322a341163d22cfad9e21f4110597fe487519697389497e4e2b
  languageName: node
  linkType: hard

"events@npm:^3.2.0":
  version: 3.3.0
  resolution: "events@npm:3.3.0"
  checksum: f6f487ad2198aa41d878fa31452f1a3c00958f46e9019286ff4787c84aac329332ab45c9cdc8c445928fc6d7ded294b9e005a7fce9426488518017831b272780
  languageName: node
  linkType: hard

"exenv-es6@npm:^1.1.1":
  version: 1.1.1
  resolution: "exenv-es6@npm:1.1.1"
  checksum: 7f2aa12025e6f06c48dc286f380cf3183bb19c6017b36d91695034a3e5124a7235c4f8ff24ca2eb88ae801322f0f99605cedfcfd996a5fcbba7669320e2a448e
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: b191531e36c607977e5b1c47811158733c34ccb3bfde92c44798929e9b4154884378536d26ad90dfecd32e1ffc09c545d23535ad91b3161a27ddbb8ebe0cbecb
  languageName: node
  linkType: hard

"fastest-levenshtein@npm:^1.0.12":
  version: 1.0.16
  resolution: "fastest-levenshtein@npm:1.0.16"
  checksum: a78d44285c9e2ae2c25f3ef0f8a73f332c1247b7ea7fb4a191e6bb51aa6ee1ef0dfb3ed113616dcdc7023e18e35a8db41f61c8d88988e877cf510df8edafbc71
  languageName: node
  linkType: hard

"find-root@npm:^1.0.0":
  version: 1.1.0
  resolution: "find-root@npm:1.1.0"
  checksum: b2a59fe4b6c932eef36c45a048ae8f93c85640212ebe8363164814990ee20f154197505965f3f4f102efc33bfb1cbc26fd17c4a2fc739ebc51b886b137cbefaf
  languageName: node
  linkType: hard

"find-up@npm:^4.0.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: ^5.0.0
    path-exists: ^4.0.0
  checksum: 4c172680e8f8c1f78839486e14a43ef82e9decd0e74145f40707cc42e7420506d5ec92d9a11c22bd2c48fb0c384ea05dd30e10dd152fefeec6f2f75282a8b844
  languageName: node
  linkType: hard

"flat@npm:^5.0.2":
  version: 5.0.2
  resolution: "flat@npm:5.0.2"
  bin:
    flat: cli.js
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"free-style@npm:3.1.0":
  version: 3.1.0
  resolution: "free-style@npm:3.1.0"
  checksum: 949258ae315deda48cac93ecd5f9a80f36e8a027e19ce2103598dc8d5ab60e963bbad5444b2a4990ddb746798dd188896f430285cf484afbf2141f7d75a191d8
  languageName: node
  linkType: hard

"fs-extra@npm:^10.1.0":
  version: 10.1.0
  resolution: "fs-extra@npm:10.1.0"
  dependencies:
    graceful-fs: ^4.2.0
    jsonfile: ^6.0.1
    universalify: ^2.0.0
  checksum: dc94ab37096f813cc3ca12f0f1b5ad6744dfed9ed21e953d72530d103cea193c2f81584a39e9dee1bea36de5ee66805678c0dddc048e8af1427ac19c00fffc50
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 99ddea01a7e75aa276c250a04eedeffe5662bce66c65c07164ad6264f9de18fb21be9433ead460e54cff20e31721c811f4fb5d70591799df5f85dce6d6746fd0
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 2b0ff4ce708d99715ad14a6d1f894e2a83242e4a52ccfcefaee5e40050562e5f6dafc1adbb4ce2d4ab47279a45dc736ab91ea5042d843c3c092820dfe032efb1
  languageName: node
  linkType: hard

"glob-to-regexp@npm:^0.4.1":
  version: 0.4.1
  resolution: "glob-to-regexp@npm:0.4.1"
  checksum: e795f4e8f06d2a15e86f76e4d92751cf8bbfcf0157cea5c2f0f35678a8195a750b34096b1256e436f0cebc1883b5ff0888c47348443e69546a5a87f9e1eb1167
  languageName: node
  linkType: hard

"glob@npm:^7.1.3":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^3.1.1
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: 29452e97b38fa704dabb1d1045350fb2467cf0277e155aa9ff7077e90ad81d1ea9d53d3ee63bd37c05b09a065e90f16aec4a65f5b8de401d1dac40bc5605d133
  languageName: node
  linkType: hard

"glob@npm:~7.1.6":
  version: 7.1.7
  resolution: "glob@npm:7.1.7"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^3.0.4
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: b61f48973bbdcf5159997b0874a2165db572b368b931135832599875919c237fc05c12984e38fe828e69aa8a921eb0e8a4997266211c517c9cfaae8a93988bb8
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.2, graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.11, graceful-fs@npm:^4.2.4":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: ac85f94da92d8eb6b7f5a8b20ce65e43d66761c55ce85ac96df6865308390da45a8d3f0296dd3a663de65d30ba497bd46c696cc1e248c72b13d6d567138a4fc7
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 4a15638b454bf086c8148979aae044dd6e39d63904cd452d970374fa6a87623423da485dfb814e7be882e05c096a7ccf1ebd48e7e7501d0208d8384ff4dea73b
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"hasown@npm:^2.0.0":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: ^1.1.2
  checksum: e8516f776a15149ca6c6ed2ae3110c417a00b62260e222590e54aa367cbcd6ed99122020b37b7fbdf05748df57b265e70095d7bf35a47660587619b15ffb93db
  languageName: node
  linkType: hard

"htmlparser2@npm:^6.0.0":
  version: 6.1.0
  resolution: "htmlparser2@npm:6.1.0"
  dependencies:
    domelementtype: ^2.0.1
    domhandler: ^4.0.0
    domutils: ^2.5.2
    entities: ^2.0.0
  checksum: 81a7b3d9c3bb9acb568a02fc9b1b81ffbfa55eae7f1c41ae0bf840006d1dbf54cb3aa245b2553e2c94db674840a9f0fdad7027c9a9d01a062065314039058c4e
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3.0.0"
  checksum: 3f60d47a5c8fc3313317edfd29a00a692cc87a19cac0159e2ce711d0ebc9019064108323b5e493625e25594f11c6236647d8e256fbe7a58f4a3b33b89e6d30bf
  languageName: node
  linkType: hard

"icss-utils@npm:^5.0.0, icss-utils@npm:^5.1.0":
  version: 5.1.0
  resolution: "icss-utils@npm:5.1.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 5c324d283552b1269cfc13a503aaaa172a280f914e5b81544f3803bc6f06a3b585fb79f66f7c771a2c052db7982c18bf92d001e3b47282e3abbbb4c4cc488d68
  languageName: node
  linkType: hard

"import-local@npm:^3.0.2":
  version: 3.1.0
  resolution: "import-local@npm:3.1.0"
  dependencies:
    pkg-dir: ^4.2.0
    resolve-cwd: ^3.0.0
  bin:
    import-local-fixture: fixtures/cli.js
  checksum: bfcdb63b5e3c0e245e347f3107564035b128a414c4da1172a20dc67db2504e05ede4ac2eee1252359f78b0bfd7b19ef180aec427c2fce6493ae782d73a04cddd
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: ^1.3.0
    wrappy: 1
  checksum: f4f76aa072ce19fae87ce1ef7d221e709afb59d445e05d47fba710e85470923a75de35bfae47da6de1b18afc3ce83d70facf44cfb0aff89f0a3f45c0a0244dfd
  languageName: node
  linkType: hard

"inherits@npm:2":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 4a48a733847879d6cf6691860a6b1e3f0f4754176e4d71494c41f3475553768b10f84b5ce1d40fbd0e34e6bfbb864ee35858ad4dd2cf31e02fc4a154b724d7f1
  languageName: node
  linkType: hard

"interpret@npm:^3.1.1":
  version: 3.1.1
  resolution: "interpret@npm:3.1.1"
  checksum: 35cebcf48c7351130437596d9ab8c8fe131ce4038da4561e6d665f25640e0034702a031cf7e3a5cea60ac7ac548bf17465e0571ede126f3d3a6933152171ac82
  languageName: node
  linkType: hard

"is-core-module@npm:^2.13.0":
  version: 2.13.1
  resolution: "is-core-module@npm:2.13.1"
  dependencies:
    hasown: ^2.0.0
  checksum: 256559ee8a9488af90e4bad16f5583c6d59e92f0742e9e8bb4331e758521ee86b810b93bae44f390766ffbc518a0488b18d9dab7da9a5ff997d499efc9403f7c
  languageName: node
  linkType: hard

"is-plain-object@npm:^2.0.4":
  version: 2.0.4
  resolution: "is-plain-object@npm:2.0.4"
  dependencies:
    isobject: ^3.0.1
  checksum: 2a401140cfd86cabe25214956ae2cfee6fbd8186809555cd0e84574f88de7b17abacb2e477a6a658fa54c6083ecbda1e6ae404c7720244cd198903848fca70ca
  languageName: node
  linkType: hard

"is-plain-object@npm:^5.0.0":
  version: 5.0.0
  resolution: "is-plain-object@npm:5.0.0"
  checksum: e32d27061eef62c0847d303125440a38660517e586f2f3db7c9d179ae5b6674ab0f469d519b2e25c147a1a3bc87156d0d5f4d8821e0ce4a9ee7fe1fcf11ce45c
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 26bf6c5480dda5161c820c5b5c751ae1e766c587b1f951ea3fcfc973bafb7831ae5b54a31a69bd670220e42e99ec154475025a468eae58ea262f813fdc8d1c62
  languageName: node
  linkType: hard

"isobject@npm:^3.0.1":
  version: 3.0.1
  resolution: "isobject@npm:3.0.1"
  checksum: db85c4c970ce30693676487cca0e61da2ca34e8d4967c2e1309143ff910c207133a969f9e4ddb2dc6aba670aabce4e0e307146c310350b298e74a31f7d464703
  languageName: node
  linkType: hard

"isomorphic.js@npm:^0.2.4":
  version: 0.2.5
  resolution: "isomorphic.js@npm:0.2.5"
  checksum: d8d1b083f05f3c337a06628b982ac3ce6db953bbef14a9de8ad49131250c3592f864b73c12030fdc9ef138ce97b76ef55c7d96a849561ac215b1b4b9d301c8e9
  languageName: node
  linkType: hard

"jest-worker@npm:^27.4.5":
  version: 27.5.1
  resolution: "jest-worker@npm:27.5.1"
  dependencies:
    "@types/node": "*"
    merge-stream: ^2.0.0
    supports-color: ^8.0.0
  checksum: 98cd68b696781caed61c983a3ee30bf880b5bd021c01d98f47b143d4362b85d0737f8523761e2713d45e18b4f9a2b98af1eaee77afade4111bb65c77d6f7c980
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 8a95213a5a77deb6cbe94d86340e8d9ace2b93bc367790b260101d2f36a2eaf4e4e22d9fa9cf459b38af3a32fb4190e638024cf82ec95ef708680e405ea7cc78
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.1":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 798ed4cf3354a2d9ccd78e86d2169515a0097a5c133337807cdf7f1fc32e1391d207ccfc276518cc1d7d8d4db93288b8a50ba4293d212ad1336e52a8ec0a941f
  languageName: node
  linkType: hard

"json-schema-compare@npm:^0.2.2":
  version: 0.2.2
  resolution: "json-schema-compare@npm:0.2.2"
  dependencies:
    lodash: ^4.17.4
  checksum: dd6f2173857c8e3b77d6ebdfa05bd505bba5b08709ab46b532722f5d1c33b5fee1fc8f3c97d0c0d011db25f9f3b0baf7ab783bb5f55c32abd9f1201760e43c2c
  languageName: node
  linkType: hard

"json-schema-merge-allof@npm:^0.8.1":
  version: 0.8.1
  resolution: "json-schema-merge-allof@npm:0.8.1"
  dependencies:
    compute-lcm: ^1.1.2
    json-schema-compare: ^0.2.2
    lodash: ^4.17.20
  checksum: 82700f6ac77351959138d6b153d77375a8c29cf48d907241b85c8292dd77aabd8cb816400f2b0d17062c4ccc8893832ec4f664ab9c814927ef502e7a595ea873
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-schema-traverse@npm:1.0.0"
  checksum: 02f2f466cdb0362558b2f1fd5e15cce82ef55d60cd7f8fa828cf35ba74330f8d767fcae5c5c2adb7851fa811766c694b9405810879bc4e1ddd78a7c0e03658ad
  languageName: node
  linkType: hard

"json5@npm:^2.1.2, json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 2a7436a93393830bce797d4626275152e37e877b265e94ca69c99e3d20c2b9dab021279146a39cdb700e71b2dd32a4cebd1514cd57cee102b1af906ce5040349
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: ^4.1.6
    universalify: ^2.0.0
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 7af3b8e1ac8fe7f1eccc6263c6ca14e1966fcbc74b618d3c78a0a2075579487547b94f72b7a1114e844a1e15bb00d440e5d1720bfc4612d790a6f285d5ea8354
  languageName: node
  linkType: hard

"jsonpointer@npm:^5.0.1":
  version: 5.0.1
  resolution: "jsonpointer@npm:5.0.1"
  checksum: 0b40f712900ad0c846681ea2db23b6684b9d5eedf55807b4708c656f5894b63507d0e28ae10aa1bddbea551241035afe62b6df0800fc94c2e2806a7f3adecd7c
  languageName: node
  linkType: hard

"kind-of@npm:^6.0.2":
  version: 6.0.3
  resolution: "kind-of@npm:6.0.3"
  checksum: 3ab01e7b1d440b22fe4c31f23d8d38b4d9b91d9f291df683476576493d5dfd2e03848a8b05813dd0c3f0e835bc63f433007ddeceb71f05cb25c45ae1b19c6d3b
  languageName: node
  linkType: hard

"lib0@npm:^0.2.85, lib0@npm:^0.2.86":
  version: 0.2.93
  resolution: "lib0@npm:0.2.93"
  dependencies:
    isomorphic.js: ^0.2.4
  bin:
    0ecdsa-generate-keypair: bin/0ecdsa-generate-keypair.js
    0gentesthtml: bin/gentesthtml.js
    0serve: bin/0serve.js
  checksum: 4c482aba249c471316fdec360ee4ace2a70ae42faad5fb6862aebb6786e187de9470eb082a5675489c59ffe54b005a15711a3d7dba33764bcab56349e61a1520
  languageName: node
  linkType: hard

"license-webpack-plugin@npm:^2.3.14":
  version: 2.3.21
  resolution: "license-webpack-plugin@npm:2.3.21"
  dependencies:
    "@types/webpack-sources": ^0.1.5
    webpack-sources: ^1.2.0
  peerDependenciesMeta:
    webpack:
      optional: true
  checksum: 6208bd2060d200fbffbcc89702c929d50c5a4a3f2158b046cf813b3f7f728bbbe4611b9fea2d67843bb5e7d64ad9122cc368a19ac73f5c4ad41765e6283bdc0c
  languageName: node
  linkType: hard

"loader-runner@npm:^4.2.0":
  version: 4.3.0
  resolution: "loader-runner@npm:4.3.0"
  checksum: a90e00dee9a16be118ea43fec3192d0b491fe03a32ed48a4132eb61d498f5536a03a1315531c19d284392a8726a4ecad71d82044c28d7f22ef62e029bf761569
  languageName: node
  linkType: hard

"loader-utils@npm:^2.0.0":
  version: 2.0.4
  resolution: "loader-utils@npm:2.0.4"
  dependencies:
    big.js: ^5.2.2
    emojis-list: ^3.0.0
    json5: ^2.1.2
  checksum: a5281f5fff1eaa310ad5e1164095689443630f3411e927f95031ab4fb83b4a98f388185bb1fe949e8ab8d4247004336a625e9255c22122b815bb9a4c5d8fc3b7
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: ^4.1.0
  checksum: 83e51725e67517287d73e1ded92b28602e3ae5580b301fe54bfb76c0c723e3f285b19252e375712316774cf52006cb236aed5704692c32db0d5d089b69696e30
  languageName: node
  linkType: hard

"lodash-es@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash-es@npm:4.17.21"
  checksum: 05cbffad6e2adbb331a4e16fbd826e7faee403a1a04873b82b42c0f22090f280839f85b95393f487c1303c8a3d2a010048bf06151a6cbe03eee4d388fb0a12d2
  languageName: node
  linkType: hard

"lodash@npm:^4.17.20, lodash@npm:^4.17.21, lodash@npm:^4.17.4, lodash@npm:^4.7.0":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: eb835a2e51d381e561e508ce932ea50a8e5a68f4ebdd771ea240d3048244a8d13658acbd502cd4829768c56f2e16bdd4340b9ea141297d472517b83868e677f7
  languageName: node
  linkType: hard

"loose-envify@npm:^1.1.0, loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: ^3.0.0 || ^4.0.0
  bin:
    loose-envify: cli.js
  checksum: 6517e24e0cad87ec9888f500c5b5947032cdfe6ef65e1c1936a0c48a524b81e65542c9c3edc91c97d5bddc806ee2a985dbc79be89215d613b1de5db6d1cfe6f4
  languageName: node
  linkType: hard

"lru-cache@npm:^6.0.0":
  version: 6.0.0
  resolution: "lru-cache@npm:6.0.0"
  dependencies:
    yallist: ^4.0.0
  checksum: f97f499f898f23e4585742138a22f22526254fdba6d75d41a1c2526b3b6cc5747ef59c5612ba7375f42aca4f8461950e925ba08c991ead0651b4918b7c978297
  languageName: node
  linkType: hard

"markdown-to-jsx@npm:^7.4.1":
  version: 7.4.4
  resolution: "markdown-to-jsx@npm:7.4.4"
  peerDependencies:
    react: ">= 0.14.0"
  checksum: a901e68a4cf258095133b659a52b35dc7b8025706d74cb363831c664cf0c948d06098b6327dd956f0f429e31d8c7f2a272a39d16c9b370072d1012557f2ade41
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 6fa4dcc8d86629705cea944a4b88ef4cb0e07656ebf223fa287443256414283dd25d91c1cd84c77987f2aec5927af1a9db6085757cb43d90eb170ebf4b47f4f4
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 0d99a03585f8b39d68182803b12ac601d9c01abfa28ec56204fa330bc9f3d1c5e14beb049bafadb3dbdf646dfb94b87e24d4ec7b31b7279ef906a8ea9b6a513f
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.27":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: 1.52.0
  checksum: 89a5b7f1def9f3af5dad6496c5ed50191ae4331cc5389d7c521c8ad28d5fdad2d06fd81baf38fed813dc4e46bb55c8145bb0ff406330818c9cf712fb2e9b3836
  languageName: node
  linkType: hard

"mini-css-extract-plugin@npm:^2.7.0":
  version: 2.8.1
  resolution: "mini-css-extract-plugin@npm:2.8.1"
  dependencies:
    schema-utils: ^4.0.0
    tapable: ^2.2.1
  peerDependencies:
    webpack: ^5.0.0
  checksum: 209f15a18cc304b0f12911927ea7e6ca4f0c3168dcc95d741811c933c4610fdb02a8486fc1a7782a6cde75c8e1880e175b7acf04e5ddfba2b8ed045d306ef04f
  languageName: node
  linkType: hard

"mini-svg-data-uri@npm:^1.4.4":
  version: 1.4.4
  resolution: "mini-svg-data-uri@npm:1.4.4"
  bin:
    mini-svg-data-uri: cli.js
  checksum: 997f1fbd8d59a70f03761e18626d335197a3479cb9d1ff75678e4b64b864d32a0b8fc18115eabde035e5299b8b4a354a78e57dd6ac10f9d604162a6170898d09
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.4, minimatch@npm:^3.1.1":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: ^1.1.7
  checksum: c154e566406683e7bcb746e000b84d74465b3a832c45d59912b9b55cd50dee66e5c4b1e5566dba26154040e51672f9aa450a9aef0c97cfc7336b78b7afb9540a
  languageName: node
  linkType: hard

"minimist@npm:~1.2.0":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 75a6d645fb122dad29c06a7597bddea977258957ed88d7a6df59b5cd3fe4a527e253e9bbf2e783e4b73657f9098b96a5fe96ab8a113655d4109108577ecf85b0
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.7":
  version: 3.3.11
  resolution: "nanoid@npm:3.3.11"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 3be20d8866a57a6b6d218e82549711c8352ed969f9ab3c45379da28f405363ad4c9aeb0b39e9abc101a529ca65a72ff9502b00bf74a912c4b64a9d62dfd26c29
  languageName: node
  linkType: hard

"neo-async@npm:^2.6.2":
  version: 2.6.2
  resolution: "neo-async@npm:2.6.2"
  checksum: deac9f8d00eda7b2e5cd1b2549e26e10a0faa70adaa6fdadca701cc55f49ee9018e427f424bac0c790b7c7e2d3068db97f3093f1093975f2acb8f8818b936ed9
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.19":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 917dbced519f48c6289a44830a0ca6dc944c3ee9243c468ebd8515a41c97c8b2c256edb7f3f750416bc37952cc9608684e6483c7b6c6f39f6bd8d86c52cfe658
  languageName: node
  linkType: hard

"object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"once@npm:^1.3.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: 1
  checksum: cd0a88501333edd640d95f0d2700fbde6bff20b3d4d9bdc521bdd31af0656b5706570d6c6afe532045a20bb8dc0849f8332d6f2a416e0ba6d3d3b98806c7db68
  languageName: node
  linkType: hard

"p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: ^2.0.0
  checksum: 84ff17f1a38126c3314e91ecfe56aecbf36430940e2873dadaa773ffe072dc23b7af8e46d4b6485d302a11673fe94c6b67ca2cfbb60c989848b02100d0594ac1
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: ^2.2.0
  checksum: 513bd14a455f5da4ebfcb819ef706c54adb09097703de6aeaa5d26fe5ea16df92b48d1ac45e01e3944ce1e6aa2a66f7f8894742b8c9d6e276e16cd2049a2b870
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: f8a8e9a7693659383f06aec604ad5ead237c7a261c18048a6e1b5b85a5f8a067e469aa24f5bc009b991ea3b058a87f5065ef4176793a200d4917349881216cae
  languageName: node
  linkType: hard

"parse-srcset@npm:^1.0.2":
  version: 1.0.2
  resolution: "parse-srcset@npm:1.0.2"
  checksum: 3a0380380c6082021fcce982f0b89fb8a493ce9dfd7d308e5e6d855201e80db8b90438649b31fdd82a3d6089a8ca17dccddaa2b730a718389af4c037b8539ebf
  languageName: node
  linkType: hard

"path-browserify@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-browserify@npm:1.0.1"
  checksum: c6d7fa376423fe35b95b2d67990060c3ee304fc815ff0a2dc1c6c3cfaff2bd0d572ee67e18f19d0ea3bbe32e8add2a05021132ac40509416459fffee35200699
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 060840f92cf8effa293bcc1bea81281bd7d363731d214cbe5c227df207c34cd727430f70c6037b5159c8a870b9157cba65e775446b0ab06fd5ecc7e54615a3b8
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0":
  version: 1.0.0
  resolution: "picocolors@npm:1.0.0"
  checksum: a2e8092dd86c8396bdba9f2b5481032848525b3dc295ce9b57896f931e63fc16f79805144321f72976383fc249584672a75cc18d6777c6b757603f372f745981
  languageName: node
  linkType: hard

"picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: e1cf46bf84886c79055fdfa9dcb3e4711ad259949e3565154b004b260cd356c5d54b31a1437ce9782624bf766272fe6b0154f5f0c744fb7af5d454d2b60db045
  languageName: node
  linkType: hard

"pkg-dir@npm:^4.2.0":
  version: 4.2.0
  resolution: "pkg-dir@npm:4.2.0"
  dependencies:
    find-up: ^4.0.0
  checksum: 9863e3f35132bf99ae1636d31ff1e1e3501251d480336edb1c211133c8d58906bed80f154a1d723652df1fda91e01c7442c2eeaf9dc83157c7ae89087e43c8d6
  languageName: node
  linkType: hard

"postcss-modules-extract-imports@npm:^3.0.0":
  version: 3.0.0
  resolution: "postcss-modules-extract-imports@npm:3.0.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 4b65f2f1382d89c4bc3c0a1bdc5942f52f3cb19c110c57bd591ffab3a5fee03fcf831604168205b0c1b631a3dce2255c70b61aaae3ef39d69cd7eb450c2552d2
  languageName: node
  linkType: hard

"postcss-modules-local-by-default@npm:^4.0.4":
  version: 4.0.4
  resolution: "postcss-modules-local-by-default@npm:4.0.4"
  dependencies:
    icss-utils: ^5.0.0
    postcss-selector-parser: ^6.0.2
    postcss-value-parser: ^4.1.0
  peerDependencies:
    postcss: ^8.1.0
  checksum: 578b955b0773147890caa88c30b10dfc849c5b1412a47ad51751890dba16fca9528c3ab00a19b186a8c2c150c2d08e2ce64d3d907800afee1f37c6d38252e365
  languageName: node
  linkType: hard

"postcss-modules-scope@npm:^3.1.1":
  version: 3.1.1
  resolution: "postcss-modules-scope@npm:3.1.1"
  dependencies:
    postcss-selector-parser: ^6.0.4
  peerDependencies:
    postcss: ^8.1.0
  checksum: 9e9d23abb0babc7fa243be65704d72a5a9ceb2bded4dbaef96a88210d468b03c8c3158c197f4e22300c851f08c6fdddd6ebe65f44e4c34448b45b8a2e063a16d
  languageName: node
  linkType: hard

"postcss-modules-values@npm:^4.0.0":
  version: 4.0.0
  resolution: "postcss-modules-values@npm:4.0.0"
  dependencies:
    icss-utils: ^5.0.0
  peerDependencies:
    postcss: ^8.1.0
  checksum: f7f2cdf14a575b60e919ad5ea52fed48da46fe80db2733318d71d523fc87db66c835814940d7d05b5746b0426e44661c707f09bdb83592c16aea06e859409db6
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^6.0.2, postcss-selector-parser@npm:^6.0.4":
  version: 6.0.16
  resolution: "postcss-selector-parser@npm:6.0.16"
  dependencies:
    cssesc: ^3.0.0
    util-deprecate: ^1.0.2
  checksum: e1cd68e33a39e3dc1e1e5bd8717be5bbe3cc23a4cecb466c3acb2f3a77daad7a47df4d6137a76f8db74cf160d2fb16b2cfdb4ccbebdfda844690f8d545fe281d
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^4.1.0, postcss-value-parser@npm:^4.2.0":
  version: 4.2.0
  resolution: "postcss-value-parser@npm:4.2.0"
  checksum: 819ffab0c9d51cf0acbabf8996dffbfafbafa57afc0e4c98db88b67f2094cb44488758f06e5da95d7036f19556a4a732525e84289a425f4f6fd8e412a9d7442f
  languageName: node
  linkType: hard

"postcss@npm:^8.3.11, postcss@npm:^8.4.33":
  version: 8.4.38
  resolution: "postcss@npm:8.4.38"
  dependencies:
    nanoid: ^3.3.7
    picocolors: ^1.0.0
    source-map-js: ^1.2.0
  checksum: 649f9e60a763ca4b5a7bbec446a069edf07f057f6d780a5a0070576b841538d1ecf7dd888f2fbfd1f76200e26c969e405aeeae66332e6927dbdc8bdcb90b9451
  languageName: node
  linkType: hard

"process@npm:^0.11.10":
  version: 0.11.10
  resolution: "process@npm:0.11.10"
  checksum: bfcce49814f7d172a6e6a14d5fa3ac92cc3d0c3b9feb1279774708a719e19acd673995226351a082a9ae99978254e320ccda4240ddc474ba31a76c79491ca7c3
  languageName: node
  linkType: hard

"prop-types@npm:^15.8.1":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: ^1.4.0
    object-assign: ^4.1.1
    react-is: ^16.13.1
  checksum: c056d3f1c057cb7ff8344c645450e14f088a915d078dcda795041765047fa080d38e5d626560ccaac94a4e16e3aa15f3557c1a9a8d1174530955e992c675e459
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0, punycode@npm:^2.1.1":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: bb0a0ceedca4c3c57a9b981b90601579058903c62be23c5e8e843d2c2d4148a3ecf029d5133486fb0e1822b098ba8bba09e89d6b21742d02fa26bda6441a6fb2
  languageName: node
  linkType: hard

"querystringify@npm:^2.1.1":
  version: 2.2.0
  resolution: "querystringify@npm:2.2.0"
  checksum: 5641ea231bad7ef6d64d9998faca95611ed4b11c2591a8cae741e178a974f6a8e0ebde008475259abe1621cb15e692404e6b6626e927f7b849d5c09392604b15
  languageName: node
  linkType: hard

"randombytes@npm:^2.1.0":
  version: 2.1.0
  resolution: "randombytes@npm:2.1.0"
  dependencies:
    safe-buffer: ^5.1.0
  checksum: d779499376bd4cbb435ef3ab9a957006c8682f343f14089ed5f27764e4645114196e75b7f6abf1cbd84fd247c0cb0651698444df8c9bf30e62120fbbc52269d6
  languageName: node
  linkType: hard

"react-dom@npm:^18.2.0":
  version: 18.2.0
  resolution: "react-dom@npm:18.2.0"
  dependencies:
    loose-envify: ^1.1.0
    scheduler: ^0.23.0
  peerDependencies:
    react: ^18.2.0
  checksum: 7d323310bea3a91be2965f9468d552f201b1c27891e45ddc2d6b8f717680c95a75ae0bc1e3f5cf41472446a2589a75aed4483aee8169287909fcd59ad149e8cc
  languageName: node
  linkType: hard

"react-is@npm:^16.13.1":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: f7a19ac3496de32ca9ae12aa030f00f14a3d45374f1ceca0af707c831b2a6098ef0d6bdae51bd437b0a306d7f01d4677fcc8de7c0d331eb47ad0f46130e53c5f
  languageName: node
  linkType: hard

"react-is@npm:^18.2.0":
  version: 18.2.0
  resolution: "react-is@npm:18.2.0"
  checksum: e72d0ba81b5922759e4aff17e0252bd29988f9642ed817f56b25a3e217e13eea8a7f2322af99a06edb779da12d5d636e9fda473d620df9a3da0df2a74141d53e
  languageName: node
  linkType: hard

"react@npm:>=17.0.0 <19.0.0, react@npm:^18.2.0":
  version: 18.2.0
  resolution: "react@npm:18.2.0"
  dependencies:
    loose-envify: ^1.1.0
  checksum: 88e38092da8839b830cda6feef2e8505dec8ace60579e46aa5490fc3dc9bba0bd50336507dc166f43e3afc1c42939c09fe33b25fae889d6f402721dcd78fca1b
  languageName: node
  linkType: hard

"rechoir@npm:^0.8.0":
  version: 0.8.0
  resolution: "rechoir@npm:0.8.0"
  dependencies:
    resolve: ^1.20.0
  checksum: ad3caed8afdefbc33fbc30e6d22b86c35b3d51c2005546f4e79bcc03c074df804b3640ad18945e6bef9ed12caedc035655ec1082f64a5e94c849ff939dc0a788
  languageName: node
  linkType: hard

"require-from-string@npm:^2.0.2":
  version: 2.0.2
  resolution: "require-from-string@npm:2.0.2"
  checksum: a03ef6895445f33a4015300c426699bc66b2b044ba7b670aa238610381b56d3f07c686251740d575e22f4c87531ba662d06937508f0f3c0f1ddc04db3130560b
  languageName: node
  linkType: hard

"requires-port@npm:^1.0.0":
  version: 1.0.0
  resolution: "requires-port@npm:1.0.0"
  checksum: eee0e303adffb69be55d1a214e415cf42b7441ae858c76dfc5353148644f6fd6e698926fc4643f510d5c126d12a705e7c8ed7e38061113bdf37547ab356797ff
  languageName: node
  linkType: hard

"resolve-cwd@npm:^3.0.0":
  version: 3.0.0
  resolution: "resolve-cwd@npm:3.0.0"
  dependencies:
    resolve-from: ^5.0.0
  checksum: 546e0816012d65778e580ad62b29e975a642989108d9a3c5beabfb2304192fa3c9f9146fbdfe213563c6ff51975ae41bac1d3c6e047dd9572c94863a057b4d81
  languageName: node
  linkType: hard

"resolve-from@npm:^5.0.0":
  version: 5.0.0
  resolution: "resolve-from@npm:5.0.0"
  checksum: 4ceeb9113e1b1372d0cd969f3468fa042daa1dd9527b1b6bb88acb6ab55d8b9cd65dbf18819f9f9ddf0db804990901dcdaade80a215e7b2c23daae38e64f5bdf
  languageName: node
  linkType: hard

"resolve@npm:^1.20.0":
  version: 1.22.8
  resolution: "resolve@npm:1.22.8"
  dependencies:
    is-core-module: ^2.13.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: f8a26958aa572c9b064562750b52131a37c29d072478ea32e129063e2da7f83e31f7f11e7087a18225a8561cfe8d2f0df9dbea7c9d331a897571c0a2527dbb4c
  languageName: node
  linkType: hard

"resolve@patch:resolve@^1.20.0#~builtin<compat/resolve>":
  version: 1.22.8
  resolution: "resolve@patch:resolve@npm%3A1.22.8#~builtin<compat/resolve>::version=1.22.8&hash=c3c19d"
  dependencies:
    is-core-module: ^2.13.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 5479b7d431cacd5185f8db64bfcb7286ae5e31eb299f4c4f404ad8aa6098b77599563ac4257cb2c37a42f59dfc06a1bec2bcf283bb448f319e37f0feb9a09847
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: ^7.1.3
  bin:
    rimraf: bin.js
  checksum: 87f4164e396f0171b0a3386cc1877a817f572148ee13a7e113b238e48e8a9f2f31d009a92ec38a591ff1567d9662c6b67fd8818a2dbbaed74bc26a87a2a4a9a0
  languageName: node
  linkType: hard

"safe-buffer@npm:^5.1.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: b99c4b41fdd67a6aaf280fcd05e9ffb0813654894223afb78a31f14a19ad220bba8aba1cb14eddce1fcfb037155fe6de4e861784eb434f7d11ed58d1e70dd491
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: cab8f25ae6f1434abee8d80023d7e72b598cf1327164ddab31003c51215526801e40b66c5e65d658a0af1e9d6478cadcb4c745f4bd6751f97d8644786c0978b0
  languageName: node
  linkType: hard

"sanitize-html@npm:~2.7.3":
  version: 2.7.3
  resolution: "sanitize-html@npm:2.7.3"
  dependencies:
    deepmerge: ^4.2.2
    escape-string-regexp: ^4.0.0
    htmlparser2: ^6.0.0
    is-plain-object: ^5.0.0
    parse-srcset: ^1.0.2
    postcss: ^8.3.11
  checksum: 2399d1fdbbc3a263fb413c1fe1971b3dc2b51abc6cc5cb49490624539d1c57a8fe31e2b21408c118e2a957f4e673e3169b1f9a5807654408f17b130a9d78aed7
  languageName: node
  linkType: hard

"scheduler@npm:^0.23.0":
  version: 0.23.0
  resolution: "scheduler@npm:0.23.0"
  dependencies:
    loose-envify: ^1.1.0
  checksum: d79192eeaa12abef860c195ea45d37cbf2bbf5f66e3c4dcd16f54a7da53b17788a70d109ee3d3dde1a0fd50e6a8fc171f4300356c5aee4fc0171de526bf35f8a
  languageName: node
  linkType: hard

"schema-utils@npm:^2.7.0":
  version: 2.7.1
  resolution: "schema-utils@npm:2.7.1"
  dependencies:
    "@types/json-schema": ^7.0.5
    ajv: ^6.12.4
    ajv-keywords: ^3.5.2
  checksum: 32c62fc9e28edd101e1bd83453a4216eb9bd875cc4d3775e4452b541908fa8f61a7bbac8ffde57484f01d7096279d3ba0337078e85a918ecbeb72872fb09fb2b
  languageName: node
  linkType: hard

"schema-utils@npm:^3.0.0, schema-utils@npm:^3.1.1":
  version: 3.3.0
  resolution: "schema-utils@npm:3.3.0"
  dependencies:
    "@types/json-schema": ^7.0.8
    ajv: ^6.12.5
    ajv-keywords: ^3.5.2
  checksum: ea56971926fac2487f0757da939a871388891bc87c6a82220d125d587b388f1704788f3706e7f63a7b70e49fc2db974c41343528caea60444afd5ce0fe4b85c0
  languageName: node
  linkType: hard

"schema-utils@npm:^4.0.0":
  version: 4.2.0
  resolution: "schema-utils@npm:4.2.0"
  dependencies:
    "@types/json-schema": ^7.0.9
    ajv: ^8.9.0
    ajv-formats: ^2.1.1
    ajv-keywords: ^5.1.0
  checksum: 26a0463d47683258106e6652e9aeb0823bf0b85843039e068b57da1892f7ae6b6b1094d48e9ed5ba5cbe9f7166469d880858b9d91abe8bd249421eb813850cde
  languageName: node
  linkType: hard

"schema-utils@npm:^4.3.0":
  version: 4.3.1
  resolution: "schema-utils@npm:4.3.1"
  dependencies:
    "@types/json-schema": ^7.0.9
    ajv: ^8.9.0
    ajv-formats: ^2.1.1
    ajv-keywords: ^5.1.0
  checksum: b470265b0c9b2bdf8b220eff6165fdfe01de2aaa479c74d3d04d596a09f86a375de7240b66a8751a647aa8ea39157c8f217d184a902886d729e8a6932ebe79ae
  languageName: node
  linkType: hard

"semver@npm:^5.4.1":
  version: 5.7.2
  resolution: "semver@npm:5.7.2"
  bin:
    semver: bin/semver
  checksum: fb4ab5e0dd1c22ce0c937ea390b4a822147a9c53dbd2a9a0132f12fe382902beef4fbf12cf51bb955248d8d15874ce8cd89532569756384f994309825f10b686
  languageName: node
  linkType: hard

"semver@npm:^7.5.4":
  version: 7.6.0
  resolution: "semver@npm:7.6.0"
  dependencies:
    lru-cache: ^6.0.0
  bin:
    semver: bin/semver.js
  checksum: 7427f05b70786c696640edc29fdd4bc33b2acf3bbe1740b955029044f80575fc664e1a512e4113c3af21e767154a94b4aa214bf6cd6e42a1f6dba5914e0b208c
  languageName: node
  linkType: hard

"serialize-javascript@npm:^6.0.1, serialize-javascript@npm:^6.0.2":
  version: 6.0.2
  resolution: "serialize-javascript@npm:6.0.2"
  dependencies:
    randombytes: ^2.1.0
  checksum: c4839c6206c1d143c0f80763997a361310305751171dd95e4b57efee69b8f6edd8960a0b7fbfc45042aadff98b206d55428aee0dc276efe54f100899c7fa8ab7
  languageName: node
  linkType: hard

"shallow-clone@npm:^3.0.0":
  version: 3.0.1
  resolution: "shallow-clone@npm:3.0.1"
  dependencies:
    kind-of: ^6.0.2
  checksum: 39b3dd9630a774aba288a680e7d2901f5c0eae7b8387fc5c8ea559918b29b3da144b7bdb990d7ccd9e11be05508ac9e459ce51d01fd65e583282f6ffafcba2e7
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: ^3.0.0
  checksum: 6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"source-list-map@npm:^2.0.0":
  version: 2.0.1
  resolution: "source-list-map@npm:2.0.1"
  checksum: 806efc6f75e7cd31e4815e7a3aaf75a45c704871ea4075cb2eb49882c6fca28998f44fc5ac91adb6de03b2882ee6fb02f951fdc85e6a22b338c32bfe19557938
  languageName: node
  linkType: hard

"source-map-js@npm:^1.2.0":
  version: 1.2.0
  resolution: "source-map-js@npm:1.2.0"
  checksum: 791a43306d9223792e84293b00458bf102a8946e7188f3db0e4e22d8d530b5f80a4ce468eb5ec0bf585443ad55ebbd630bf379c98db0b1f317fd902500217f97
  languageName: node
  linkType: hard

"source-map-loader@npm:~1.0.2":
  version: 1.0.2
  resolution: "source-map-loader@npm:1.0.2"
  dependencies:
    data-urls: ^2.0.0
    iconv-lite: ^0.6.2
    loader-utils: ^2.0.0
    schema-utils: ^2.7.0
    source-map: ^0.6.1
  peerDependencies:
    webpack: ^4.0.0 || ^5.0.0
  checksum: 0360b536e904f8fea452d0e122b9199661765229dc62a4b8093cc9d14e985f2ddd146355ede6d11acdd0b9bf4639b364e2526afcf9d3218ed45af63aa5eb053f
  languageName: node
  linkType: hard

"source-map-support@npm:~0.5.20":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: ^1.0.0
    source-map: ^0.6.0
  checksum: 43e98d700d79af1d36f859bdb7318e601dfc918c7ba2e98456118ebc4c4872b327773e5a1df09b0524e9e5063bb18f0934538eace60cca2710d1fa687645d137
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0, source-map@npm:^0.6.1, source-map@npm:~0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 59ce8640cf3f3124f64ac289012c2b8bd377c238e316fb323ea22fbfe83da07d81e000071d7242cad7a23cd91c7de98e4df8830ec3f133cb6133a5f6e9f67bc2
  languageName: node
  linkType: hard

"style-loader@npm:~3.3.1":
  version: 3.3.4
  resolution: "style-loader@npm:3.3.4"
  peerDependencies:
    webpack: ^5.0.0
  checksum: caac3f2fe2c3c89e49b7a2a9329e1cfa515ecf5f36b9c4885f9b218019fda207a9029939b2c35821dec177a264a007e7c391ccdd3ff7401881ce6287b9c8f38b
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: ^3.0.0
  checksum: 95f6f4ba5afdf92f495b5a912d4abee8dcba766ae719b975c56c084f5004845f6f5a5f7769f52d53f40e21952a6d87411bafe34af4a01e65f9926002e38e1dac
  languageName: node
  linkType: hard

"supports-color@npm:^7.2.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: ^4.0.0
  checksum: 3dda818de06ebbe5b9653e07842d9479f3555ebc77e9a0280caf5a14fb877ffee9ed57007c3b78f5a6324b8dbeec648d9e97a24e2ed9fdb81ddc69ea07100f4a
  languageName: node
  linkType: hard

"supports-color@npm:^8.0.0":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: ^4.0.0
  checksum: c052193a7e43c6cdc741eb7f378df605636e01ad434badf7324f17fb60c69a880d8d8fcdcb562cf94c2350e57b937d7425ab5b8326c67c2adc48f7c87c1db406
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 53b1e247e68e05db7b3808b99b892bd36fb096e6fba213a06da7fab22045e97597db425c724f2bbd6c99a3c295e1e73f3e4de78592289f38431049e1277ca0ae
  languageName: node
  linkType: hard

"tabbable@npm:^5.2.0":
  version: 5.3.3
  resolution: "tabbable@npm:5.3.3"
  checksum: 1aa56e1bb617cc10616c407f4e756f0607f3e2d30f9803664d70b85db037ca27e75918ed1c71443f3dc902e21dc9f991ce4b52d63a538c9b69b3218d3babcd70
  languageName: node
  linkType: hard

"tapable@npm:^2.1.1, tapable@npm:^2.2.0, tapable@npm:^2.2.1":
  version: 2.2.1
  resolution: "tapable@npm:2.2.1"
  checksum: 3b7a1b4d86fa940aad46d9e73d1e8739335efd4c48322cb37d073eb6f80f5281889bf0320c6d8ffcfa1a0dd5bfdbd0f9d037e252ef972aca595330538aac4d51
  languageName: node
  linkType: hard

"terser-webpack-plugin@npm:^5.3.11":
  version: 5.3.14
  resolution: "terser-webpack-plugin@npm:5.3.14"
  dependencies:
    "@jridgewell/trace-mapping": ^0.3.25
    jest-worker: ^27.4.5
    schema-utils: ^4.3.0
    serialize-javascript: ^6.0.2
    terser: ^5.31.1
  peerDependencies:
    webpack: ^5.1.0
  peerDependenciesMeta:
    "@swc/core":
      optional: true
    esbuild:
      optional: true
    uglify-js:
      optional: true
  checksum: 13a1e67f1675a473b18d25cb0ce65c3f0a19b5e9a93213a99ea61dc4ca996ea93aa17a221965b526f5788d242836a8249ad00538fbb322e25cb69076eb55feab
  languageName: node
  linkType: hard

"terser-webpack-plugin@npm:^5.3.7":
  version: 5.3.10
  resolution: "terser-webpack-plugin@npm:5.3.10"
  dependencies:
    "@jridgewell/trace-mapping": ^0.3.20
    jest-worker: ^27.4.5
    schema-utils: ^3.1.1
    serialize-javascript: ^6.0.1
    terser: ^5.26.0
  peerDependencies:
    webpack: ^5.1.0
  peerDependenciesMeta:
    "@swc/core":
      optional: true
    esbuild:
      optional: true
    uglify-js:
      optional: true
  checksum: bd6e7596cf815f3353e2a53e79cbdec959a1b0276f5e5d4e63e9d7c3c5bb5306df567729da287d1c7b39d79093e56863c569c42c6c24cc34c76aa313bd2cbcea
  languageName: node
  linkType: hard

"terser@npm:^5.26.0":
  version: 5.29.2
  resolution: "terser@npm:5.29.2"
  dependencies:
    "@jridgewell/source-map": ^0.3.3
    acorn: ^8.8.2
    commander: ^2.20.0
    source-map-support: ~0.5.20
  bin:
    terser: bin/terser
  checksum: 2310d04e530903ed4da6168c4c90ab65965c5f1f8919733772119ff560e9e9be2def070c9659f7d96f2e28489c4378241c4cef1917f05b9524587436fdd5a802
  languageName: node
  linkType: hard

"terser@npm:^5.31.1":
  version: 5.39.0
  resolution: "terser@npm:5.39.0"
  dependencies:
    "@jridgewell/source-map": ^0.3.3
    acorn: ^8.8.2
    commander: ^2.20.0
    source-map-support: ~0.5.20
  bin:
    terser: bin/terser
  checksum: e39c302aed7a70273c8b03032c37c68c8d9d3b432a7b6abe89caf9d087f7dd94d743c01ee5ba1431a095ad347c4a680b60d258f298a097cf512346d6041eb661
  languageName: node
  linkType: hard

"test-hyphens-underscore@workspace:.":
  version: 0.0.0-use.local
  resolution: "test-hyphens-underscore@workspace:."
  dependencies:
    "@jupyterlab/builder": ^4.0.0
    "@jupyterlab/launcher": ^4.0.0
    rimraf: ^3.0.2
  languageName: unknown
  linkType: soft

"tr46@npm:^2.1.0":
  version: 2.1.0
  resolution: "tr46@npm:2.1.0"
  dependencies:
    punycode: ^2.1.1
  checksum: ffe6049b9dca3ae329b059aada7f515b0f0064c611b39b51ff6b53897e954650f6f63d9319c6c008d36ead477c7b55e5f64c9dc60588ddc91ff720d64eb710b3
  languageName: node
  linkType: hard

"tslib@npm:^1.13.0":
  version: 1.14.1
  resolution: "tslib@npm:1.14.1"
  checksum: dbe628ef87f66691d5d2959b3e41b9ca0045c3ee3c7c7b906cc1e328b39f199bb1ad9e671c39025bd56122ac57dfbf7385a94843b1cc07c60a4db74795829acd
  languageName: node
  linkType: hard

"typestyle@npm:^2.0.4":
  version: 2.4.0
  resolution: "typestyle@npm:2.4.0"
  dependencies:
    csstype: 3.0.10
    free-style: 3.1.0
  checksum: 8b4f02c24f67b594f98507b15a753dabd4db5eb0af007e1d310527c64030e11e9464b25b5a6bc65fb5eec9a4459a8336050121ecc29063ac87b8b47a6d698893
  languageName: node
  linkType: hard

"undici-types@npm:~5.26.4":
  version: 5.26.5
  resolution: "undici-types@npm:5.26.5"
  checksum: 3192ef6f3fd5df652f2dc1cd782b49d6ff14dc98e5dced492aa8a8c65425227da5da6aafe22523c67f035a272c599bb89cfe803c1db6311e44bed3042fc25487
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.1
  resolution: "universalify@npm:2.0.1"
  checksum: ecd8469fe0db28e7de9e5289d32bd1b6ba8f7183db34f3bfc4ca53c49891c2d6aa05f3fb3936a81285a905cc509fb641a0c3fc131ec786167eff41236ae32e60
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.1":
  version: 1.1.3
  resolution: "update-browserslist-db@npm:1.1.3"
  dependencies:
    escalade: ^3.2.0
    picocolors: ^1.1.1
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 7b6d8d08c34af25ee435bccac542bedcb9e57c710f3c42421615631a80aa6dd28b0a81c9d2afbef53799d482fb41453f714b8a7a0a8003e3b4ec8fb1abb819af
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: ^2.1.0
  checksum: 7167432de6817fe8e9e0c9684f1d2de2bb688c94388f7569f7dbdb1587c9f4ca2a77962f134ec90be0cc4d004c939ff0d05acc9f34a0db39a3c797dada262633
  languageName: node
  linkType: hard

"url-parse@npm:~1.5.4":
  version: 1.5.10
  resolution: "url-parse@npm:1.5.10"
  dependencies:
    querystringify: ^2.1.1
    requires-port: ^1.0.0
  checksum: fbdba6b1d83336aca2216bbdc38ba658d9cfb8fc7f665eb8b17852de638ff7d1a162c198a8e4ed66001ddbf6c9888d41e4798912c62b4fd777a31657989f7bdf
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.2":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"validate.io-array@npm:^1.0.3":
  version: 1.0.6
  resolution: "validate.io-array@npm:1.0.6"
  checksum: 54eca83ebc702e3e46499f9d9e77287a95ae25c4e727cd2fafee29c7333b3a36cca0c5d8f090b9406262786de80750fba85e7e7ef41e20bf8cc67d5570de449b
  languageName: node
  linkType: hard

"validate.io-function@npm:^1.0.2":
  version: 1.0.2
  resolution: "validate.io-function@npm:1.0.2"
  checksum: e4cce2479a20cb7c42e8630c777fb107059c27bc32925f769e3a73ca5fd62b4892d897b3c80227e14d5fcd1c5b7d05544e0579d63e59f14034c0052cda7f7c44
  languageName: node
  linkType: hard

"validate.io-integer-array@npm:^1.0.0":
  version: 1.0.0
  resolution: "validate.io-integer-array@npm:1.0.0"
  dependencies:
    validate.io-array: ^1.0.3
    validate.io-integer: ^1.0.4
  checksum: 5f6d7fab8df7d2bf546a05e830201768464605539c75a2c2417b632b4411a00df84b462f81eac75e1be95303e7e0ac92f244c137424739f4e15cd21c2eb52c7f
  languageName: node
  linkType: hard

"validate.io-integer@npm:^1.0.4":
  version: 1.0.5
  resolution: "validate.io-integer@npm:1.0.5"
  dependencies:
    validate.io-number: ^1.0.3
  checksum: 88b3f8bb5a5277a95305d64abbfc437079220ce4f57a148cc6113e7ccec03dd86b10a69d413982602aa90a62b8d516148a78716f550dcd3aff863ac1c2a7a5e6
  languageName: node
  linkType: hard

"validate.io-number@npm:^1.0.3":
  version: 1.0.3
  resolution: "validate.io-number@npm:1.0.3"
  checksum: 42418aeb6c969efa745475154fe576809b02eccd0961aad0421b090d6e7a12d23a3e28b0d5dddd2c6347c1a6bdccb82bba5048c716131cd20207244d50e07282
  languageName: node
  linkType: hard

"watchpack@npm:^2.4.1":
  version: 2.4.1
  resolution: "watchpack@npm:2.4.1"
  dependencies:
    glob-to-regexp: ^0.4.1
    graceful-fs: ^4.1.2
  checksum: 5b0179348655dcdf19cac7cb4ff923fdc024d630650c0bf6bec8899cf47c60e19d4f810a88dba692ed0e7f684cf0fcffea86efdbf6c35d81f031e328043b7fab
  languageName: node
  linkType: hard

"webidl-conversions@npm:^6.1.0":
  version: 6.1.0
  resolution: "webidl-conversions@npm:6.1.0"
  checksum: 1f526507aa491f972a0c1409d07f8444e1d28778dfa269a9971f2e157182f3d496dc33296e4ed45b157fdb3bf535bb90c90bf10c50dcf1dd6caacb2a34cc84fb
  languageName: node
  linkType: hard

"webpack-cli@npm:^5.0.1":
  version: 5.1.4
  resolution: "webpack-cli@npm:5.1.4"
  dependencies:
    "@discoveryjs/json-ext": ^0.5.0
    "@webpack-cli/configtest": ^2.1.1
    "@webpack-cli/info": ^2.0.2
    "@webpack-cli/serve": ^2.0.5
    colorette: ^2.0.14
    commander: ^10.0.1
    cross-spawn: ^7.0.3
    envinfo: ^7.7.3
    fastest-levenshtein: ^1.0.12
    import-local: ^3.0.2
    interpret: ^3.1.1
    rechoir: ^0.8.0
    webpack-merge: ^5.7.3
  peerDependencies:
    webpack: 5.x.x
  peerDependenciesMeta:
    "@webpack-cli/generators":
      optional: true
    webpack-bundle-analyzer:
      optional: true
    webpack-dev-server:
      optional: true
  bin:
    webpack-cli: bin/cli.js
  checksum: 3a4ad0d0342a6815c850ee4633cc2a8a5dae04f918e7847f180bf24ab400803cf8a8943707ffbed03eb20fe6ce647f996f60a2aade87b0b4a9954da3da172ce0
  languageName: node
  linkType: hard

"webpack-merge@npm:^5.7.3, webpack-merge@npm:^5.8.0":
  version: 5.10.0
  resolution: "webpack-merge@npm:5.10.0"
  dependencies:
    clone-deep: ^4.0.1
    flat: ^5.0.2
    wildcard: ^2.0.0
  checksum: 1fe8bf5309add7298e1ac72fb3f2090e1dfa80c48c7e79fa48aa60b5961332c7d0d61efa8851acb805e6b91a4584537a347bc106e05e9aec87fa4f7088c62f2f
  languageName: node
  linkType: hard

"webpack-sources@npm:^1.2.0":
  version: 1.4.3
  resolution: "webpack-sources@npm:1.4.3"
  dependencies:
    source-list-map: ^2.0.0
    source-map: ~0.6.1
  checksum: 37463dad8d08114930f4bc4882a9602941f07c9f0efa9b6bc78738cd936275b990a596d801ef450d022bb005b109b9f451dd087db2f3c9baf53e8e22cf388f79
  languageName: node
  linkType: hard

"webpack-sources@npm:^3.2.3":
  version: 3.2.3
  resolution: "webpack-sources@npm:3.2.3"
  checksum: 989e401b9fe3536529e2a99dac8c1bdc50e3a0a2c8669cbafad31271eadd994bc9405f88a3039cd2e29db5e6d9d0926ceb7a1a4e7409ece021fe79c37d9c4607
  languageName: node
  linkType: hard

"webpack@npm:^5.76.1":
  version: 5.99.6
  resolution: "webpack@npm:5.99.6"
  dependencies:
    "@types/eslint-scope": ^3.7.7
    "@types/estree": ^1.0.6
    "@webassemblyjs/ast": ^1.14.1
    "@webassemblyjs/wasm-edit": ^1.14.1
    "@webassemblyjs/wasm-parser": ^1.14.1
    acorn: ^8.14.0
    browserslist: ^4.24.0
    chrome-trace-event: ^1.0.2
    enhanced-resolve: ^5.17.1
    es-module-lexer: ^1.2.1
    eslint-scope: 5.1.1
    events: ^3.2.0
    glob-to-regexp: ^0.4.1
    graceful-fs: ^4.2.11
    json-parse-even-better-errors: ^2.3.1
    loader-runner: ^4.2.0
    mime-types: ^2.1.27
    neo-async: ^2.6.2
    schema-utils: ^4.3.0
    tapable: ^2.1.1
    terser-webpack-plugin: ^5.3.11
    watchpack: ^2.4.1
    webpack-sources: ^3.2.3
  peerDependenciesMeta:
    webpack-cli:
      optional: true
  bin:
    webpack: bin/webpack.js
  checksum: 3be2e88c874cde705aefae11715d290c9b4cf3d678d435736c6f67772db5a365c312a2c8dac7dc1d4c6f56b1df8966ad4b46a69f77914595e46cce6947327521
  languageName: node
  linkType: hard

"whatwg-mimetype@npm:^2.3.0":
  version: 2.3.0
  resolution: "whatwg-mimetype@npm:2.3.0"
  checksum: 23eb885940bcbcca4ff841c40a78e9cbb893ec42743993a42bf7aed16085b048b44b06f3402018931687153550f9a32d259dfa524e4f03577ab898b6965e5383
  languageName: node
  linkType: hard

"whatwg-url@npm:^8.0.0":
  version: 8.7.0
  resolution: "whatwg-url@npm:8.7.0"
  dependencies:
    lodash: ^4.7.0
    tr46: ^2.1.0
    webidl-conversions: ^6.1.0
  checksum: a87abcc6cefcece5311eb642858c8fdb234e51ec74196bfacf8def2edae1bfbffdf6acb251646ed6301f8cee44262642d8769c707256125a91387e33f405dd1e
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: ^2.0.0
  bin:
    node-which: ./bin/node-which
  checksum: 1a5c563d3c1b52d5f893c8b61afe11abc3bab4afac492e8da5bde69d550de701cf9806235f20a47b5c8fa8a1d6a9135841de2596535e998027a54589000e66d1
  languageName: node
  linkType: hard

"wildcard@npm:^2.0.0":
  version: 2.0.1
  resolution: "wildcard@npm:2.0.1"
  checksum: e0c60a12a219e4b12065d1199802d81c27b841ed6ad6d9d28240980c73ceec6f856771d575af367cbec2982d9ae7838759168b551776577f155044f5a5ba843c
  languageName: node
  linkType: hard

"worker-loader@npm:^3.0.2":
  version: 3.0.8
  resolution: "worker-loader@npm:3.0.8"
  dependencies:
    loader-utils: ^2.0.0
    schema-utils: ^3.0.0
  peerDependencies:
    webpack: ^4.0.0 || ^5.0.0
  checksum: 84f4a7eeb2a1d8b9704425837e017c91eedfae67ac89e0b866a2dcf283323c1dcabe0258196278b7d5fd0041392da895c8a0c59ddf3a94f1b2e003df68ddfec3
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 159da4805f7e84a3d003d8841557196034155008f817172d4e986bd591f74aa82aa7db55929a54222309e01079a65a92a9e6414da5a6aa4b01ee44a511ac3ee5
  languageName: node
  linkType: hard

"ws@npm:^8.11.0":
  version: 8.16.0
  resolution: "ws@npm:8.16.0"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: feb3eecd2bae82fa8a8beef800290ce437d8b8063bdc69712725f21aef77c49cb2ff45c6e5e7fce622248f9c7abaee506bae0a9064067ffd6935460c7357321b
  languageName: node
  linkType: hard

"y-protocols@npm:^1.0.5":
  version: 1.0.6
  resolution: "y-protocols@npm:1.0.6"
  dependencies:
    lib0: ^0.2.85
  peerDependencies:
    yjs: ^13.0.0
  checksum: 4b57c8811befcf2e45c3d47830005f8a33e626c734f78a42fe8a4fa3caad2233ba85a7c8bceefbd52ffc40130d3f3faee664fd0d1c324ff1fa8817a056ccdc1c
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 343617202af32df2a15a3be36a5a8c0c8545208f3d3dfbc6bb7c3e3b7e8c6f8e7485432e4f3b88da3031a6e20afa7c711eded32ddfb122896ac5d914e75848d5
  languageName: node
  linkType: hard

"yjs@npm:^13.5.40":
  version: 13.6.14
  resolution: "yjs@npm:13.6.14"
  dependencies:
    lib0: ^0.2.86
  checksum: df399049049820d32d5759a7bd9d70cf30602408ca2a9771324f1b459f703bb6073fb35b5bcde7493fab3721d64e3c1b60eb88415b184e95a73fbce2947741cb
  languageName: node
  linkType: hard
